<?php
session_start();
require_once 'includes/db_connect.php';

echo "<h2>🔍 Product Debug Page</h2>";

// Check database connection
if (!isset($conn)) {
    echo "<div style='color: red;'>❌ Database connection not available</div>";
    exit;
}

echo "<div style='color: green;'>✅ Database connection OK</div>";

// Check if we have any products
try {
    $stmt = $conn->prepare("SELECT product_id, NAME, stock, price FROM products ORDER BY product_id LIMIT 10");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($products) {
        echo "<h3>Available Products:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Stock</th><th>Price</th><th>Action</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product['product_id'] . "</td>";
            echo "<td>" . htmlspecialchars($product['NAME']) . "</td>";
            echo "<td>" . $product['stock'] . "</td>";
            echo "<td>Rp " . number_format($product['price']) . "</td>";
            echo "<td><a href='product-detail.php?id=" . $product['product_id'] . "' target='_blank'>View Detail</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<div style='color: red;'>❌ No products found in database</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database error: " . $e->getMessage() . "</div>";
}

// Test specific product ID
$test_id = $_GET['id'] ?? 1;
echo "<h3>Testing Product ID: $test_id</h3>";

try {
    $stmt = $conn->prepare("SELECT * FROM products WHERE product_id = ?");
    $stmt->execute([$test_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($product) {
        echo "<div style='color: green;'>✅ Product found:</div>";
        echo "<pre>" . print_r($product, true) . "</pre>";
    } else {
        echo "<div style='color: red;'>❌ Product with ID $test_id not found</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error testing product: " . $e->getMessage() . "</div>";
}
?>
