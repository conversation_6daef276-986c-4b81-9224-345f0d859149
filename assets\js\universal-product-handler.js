/**
 * Universal Product Detail Handler
 * Handles quantity controls and add to cart functionality across all product pages
 */

console.log('🌟 Universal Product Handler loaded');

class UniversalProductHandler {
    constructor() {
        this.init();
    }

    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupHandlers());
        } else {
            this.setupHandlers();
        }
    }

    setupHandlers() {
        console.log('🔧 Setting up universal product handlers');
        
        // Setup quantity controls
        this.setupQuantityControls();
        
        // Setup add to cart buttons
        this.setupAddToCartButtons();
        
        // Setup modal handlers
        this.setupModalHandlers();
        
        // Add CSS for better styling
        this.addStyles();
        
        console.log('✅ Universal product handlers ready');
    }

    setupQuantityControls() {
        // Handle all quantity selectors (both in modals and detail pages)
        document.addEventListener('click', (e) => {
            // Handle decrease buttons
            if (e.target.matches('.decrease, #decrease-quantity, .btn-decrease') || 
                e.target.closest('.decrease, #decrease-quantity, .btn-decrease')) {
                
                e.preventDefault();
                e.stopPropagation();
                
                const button = e.target.matches('.decrease, #decrease-quantity, .btn-decrease') ? 
                              e.target : e.target.closest('.decrease, #decrease-quantity, .btn-decrease');
                
                this.handleQuantityDecrease(button);
            }
            
            // Handle increase buttons
            if (e.target.matches('.increase, #increase-quantity, .btn-increase') || 
                e.target.closest('.increase, #increase-quantity, .btn-increase')) {
                
                e.preventDefault();
                e.stopPropagation();
                
                const button = e.target.matches('.increase, #increase-quantity, .btn-increase') ? 
                              e.target : e.target.closest('.increase, #increase-quantity, .btn-increase');
                
                this.handleQuantityIncrease(button);
            }
        });

        // Handle direct input changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.qty-input, #quantity, .quantity-input')) {
                this.validateQuantityInput(e.target);
            }
        });
    }

    handleQuantityDecrease(button) {
        const container = button.closest('.quantity-selector, .input-group, .product-detail');
        const input = container.querySelector('.qty-input, #quantity, .quantity-input');
        
        if (!input) return;
        
        let value = parseInt(input.value) || 1;
        const min = parseInt(input.getAttribute('min')) || 1;
        
        if (value > min) {
            input.value = value - 1;
            this.addQuantityAnimation(input);
            console.log('Quantity decreased to:', input.value);
        } else {
            this.showNotification(`Minimum quantity is ${min}`, 'warning');
        }
    }

    handleQuantityIncrease(button) {
        const container = button.closest('.quantity-selector, .input-group, .product-detail');
        const input = container.querySelector('.qty-input, #quantity, .quantity-input');
        
        if (!input) return;
        
        let value = parseInt(input.value) || 1;
        const max = parseInt(input.getAttribute('max')) || 999;
        
        if (value < max) {
            input.value = value + 1;
            this.addQuantityAnimation(input);
            console.log('Quantity increased to:', input.value);
        } else {
            this.showNotification(`Maximum stock available: ${max}`, 'warning');
        }
    }

    validateQuantityInput(input) {
        let value = parseInt(input.value) || 1;
        const min = parseInt(input.getAttribute('min')) || 1;
        const max = parseInt(input.getAttribute('max')) || 999;
        
        if (value < min) {
            input.value = min;
            this.showNotification(`Minimum quantity is ${min}`, 'warning');
        } else if (value > max) {
            input.value = max;
            this.showNotification(`Maximum stock available: ${max}`, 'warning');
        }
    }

    addQuantityAnimation(input) {
        input.classList.add('quantity-changed');
        setTimeout(() => input.classList.remove('quantity-changed'), 300);
    }

    setupAddToCartButtons() {
        document.addEventListener('click', (e) => {
            // Handle all add to cart buttons
            if (e.target.matches('.add-to-cart-btn, .btn-add-to-cart, .add-to-cart') || 
                e.target.closest('.add-to-cart-btn, .btn-add-to-cart, .add-to-cart')) {
                
                e.preventDefault();
                e.stopPropagation();
                
                const button = e.target.matches('.add-to-cart-btn, .btn-add-to-cart, .add-to-cart') ? 
                              e.target : e.target.closest('.add-to-cart-btn, .btn-add-to-cart, .add-to-cart');
                
                this.handleAddToCart(button);
            }
        });
    }

    handleAddToCart(button) {
        if (button.disabled) {
            console.log('Button already disabled, ignoring click');
            return;
        }

        console.log('🛒 Add to cart clicked');
        
        // Get product information
        const productId = this.getProductId(button);
        const quantity = this.getQuantity(button);
        
        if (!productId) {
            this.showNotification('Product ID not found', 'error');
            return;
        }

        console.log('Adding to cart:', { productId, quantity });
        
        // Set loading state
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
        
        // Send request
        this.sendAddToCartRequest(productId, quantity)
            .then(data => {
                if (data.success) {
                    this.handleAddToCartSuccess(button, originalText, data);
                } else {
                    this.handleAddToCartError(button, originalText, data.message);
                }
            })
            .catch(error => {
                this.handleAddToCartError(button, originalText, error.message);
            });
    }

    getProductId(button) {
        // Try multiple ways to get product ID
        return button.dataset.productId || 
               button.getAttribute('data-product-id') ||
               button.closest('[data-product-id]')?.dataset.productId ||
               button.closest('.product-card, .product-detail')?.dataset.productId;
    }

    getQuantity(button) {
        // Try to find quantity input in the same container
        const container = button.closest('.product-card, .product-detail, .modal-body, .card-body');
        const quantityInput = container?.querySelector('.qty-input, #quantity, .quantity-input');
        
        return quantityInput ? parseInt(quantityInput.value) || 1 : 1;
    }

    async sendAddToCartRequest(productId, quantity) {
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        const response = await fetch('ajax/simple_add_to_cart.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return await response.json();
    }

    handleAddToCartSuccess(button, originalText, data) {
        // Success feedback
        button.innerHTML = '<i class="fas fa-check"></i> Added!';
        button.classList.add('btn-success');
        
        // Update cart count
        this.updateCartCount(data.cart_count);
        
        // Show success notification
        const message = `${data.product_name || 'Product'} (${data.quantity || 1}) added to cart!`;
        this.showNotification(message, 'success');
        
        // Reset button after 3 seconds
        setTimeout(() => {
            button.innerHTML = originalText;
            button.classList.remove('btn-success');
            button.disabled = false;
        }, 3000);
    }

    handleAddToCartError(button, originalText, message) {
        console.error('Add to cart failed:', message);
        
        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show error notification
        this.showNotification(message || 'Failed to add product to cart', 'error');
    }

    updateCartCount(count) {
        const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count');
        cartElements.forEach(element => {
            element.textContent = count;
            if (count > 0) {
                element.style.display = 'inline';
                element.classList.add('animate-bounce');
                setTimeout(() => element.classList.remove('animate-bounce'), 600);
            } else {
                element.style.display = 'none';
            }
        });
    }

    setupModalHandlers() {
        // Handle modal-specific functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn-detail') || e.target.closest('.btn-detail')) {
                // Modal detail button clicked - ensure quantity controls work in modal
                setTimeout(() => {
                    this.initializeModalQuantityControls();
                }, 100);
            }
        });
    }

    initializeModalQuantityControls() {
        // Initialize quantity controls for newly opened modals
        const openModals = document.querySelectorAll('.modal[style*="display: block"], .product-modal[style*="display: block"]');
        openModals.forEach(modal => {
            const quantityInput = modal.querySelector('.qty-input, #quantity, .quantity-input');
            if (quantityInput && !quantityInput.dataset.initialized) {
                quantityInput.dataset.initialized = 'true';
                quantityInput.value = 1; // Reset to 1
            }
        });
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        document.querySelectorAll('.universal-notification').forEach(n => n.remove());
        
        // Create notification
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} universal-notification`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
        `;
        
        const icon = type === 'success' ? 'check-circle' : 
                    type === 'error' ? 'exclamation-circle' : 
                    type === 'warning' ? 'exclamation-triangle' : 'info-circle';
        
        notification.innerHTML = `
            <i class="fas fa-${icon} me-2"></i>${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Auto remove after 4 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, 4000);
    }

    addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .quantity-changed {
                background-color: #e3f2fd !important;
                transition: background-color 0.3s ease;
            }
            
            .animate-bounce {
                animation: bounce 0.6s ease;
            }
            
            @keyframes bounce {
                0%, 20%, 60%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                80% { transform: translateY(-5px); }
            }
            
            .btn-success {
                background-color: #28a745 !important;
                border-color: #28a745 !important;
                color: white !important;
            }
            
            /* Ensure buttons are clickable */
            .decrease, .increase, #decrease-quantity, #increase-quantity, 
            .add-to-cart-btn, .btn-add-to-cart, .add-to-cart {
                cursor: pointer !important;
                pointer-events: auto !important;
                position: relative !important;
                z-index: 10 !important;
            }
            
            .universal-notification {
                pointer-events: auto;
            }
        `;
        document.head.appendChild(style);
    }
}

// Initialize the universal handler
const universalProductHandler = new UniversalProductHandler();

// Make it globally available
window.UniversalProductHandler = UniversalProductHandler;
window.universalProductHandler = universalProductHandler;

console.log('✅ Universal Product Handler initialized');
