<?php
// Optimized products page with performance improvements
ob_start(); // Start output buffering for better performance
session_start();

// Auto-login for testing (remove in production)
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_name'] = 'Test User';
    $_SESSION['user_email'] = '<EMAIL>';
}

// Set page variables for header
$page = 'products';
$page_title = '';

// Load konfigurasi dan fungsi
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';

// Helper function to build pagination URLs
function buildPaginationUrl($page, $category_id = null, $searchTerm = '', $sort_by = '') {
    $params = array();

    if ($page > 1) {
        $params['page'] = $page;
    }

    if ($category_id) {
        $params['category'] = $category_id;
    }

    if (!empty($searchTerm)) {
        $params['search'] = $searchTerm;
    }

    if (!empty($sort_by) && $sort_by !== 'name_asc') {
        $params['sort'] = $sort_by;
    }

    $url = 'products_public.php';
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    return $url;
}

// Enable caching headers for static content
header('Cache-Control: public, max-age=300'); // 5 minutes cache

// Optimized category fetching with product counts
$categories = [];
$cache_key = 'categories_list';

// Try to get from cache first (if you have caching system)
try {
    if (isset($conn) && $conn) {
        // Use optimized query with product counts
        $categoryQuery = "
            SELECT c.category_id, c.name, c.slug, c.description,
                   COUNT(p.product_id) as product_count
            FROM categories c
            LEFT JOIN products p ON c.category_id = p.category_id AND p.is_active = 1
            GROUP BY c.category_id, c.name, c.slug, c.description
            ORDER BY c.name
            LIMIT 20
        ";
        $categoryStmt = $conn->prepare($categoryQuery);
        $categoryStmt->execute();
        $categories = $categoryStmt->fetchAll(PDO::FETCH_ASSOC);

        // If no categories found, insert default ones (only once)
        if (empty($categories)) {
            $defaultCategories = [
                ['name' => 'Electronics', 'slug' => 'electronics', 'description' => 'Electronic devices and gadgets'],
                ['name' => 'Fashion', 'slug' => 'fashion', 'description' => 'Clothing and accessories'],
                ['name' => 'Home & Garden', 'slug' => 'home-garden', 'description' => 'Home and garden products'],
                ['name' => 'Sports', 'slug' => 'sports', 'description' => 'Sports and fitness equipment'],
                ['name' => 'Health', 'slug' => 'health', 'description' => 'Health and wellness products']
            ];

            $insertStmt = $conn->prepare("INSERT IGNORE INTO categories (name, slug, description) VALUES (?, ?, ?)");
            foreach ($defaultCategories as $cat) {
                $insertStmt->execute([$cat['name'], $cat['slug'], $cat['description']]);
            }

            // Fetch again after insert
            $categoryStmt->execute();
            $categories = $categoryStmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
} catch (PDOException $e) {
    error_log("Error fetching categories: " . $e->getMessage());
}

// Fallback categories if database still fails
if (empty($categories)) {
    $categories = [
        ['category_id' => 1, 'name' => 'Cosmetics', 'slug' => 'cosmetics'],
        ['category_id' => 2, 'name' => 'Medicine', 'slug' => 'medicine'],
        ['category_id' => 3, 'name' => 'Milk Products', 'slug' => 'milk-products'],
        ['category_id' => 4, 'name' => 'Sports', 'slug' => 'sports'],
        ['category_id' => 5, 'name' => 'Vegetables', 'slug' => 'vegetables']
    ];
}

// Optimized product filtering with prepared parameters
$category_id = isset($_GET['category']) && is_numeric($_GET['category']) ? (int)$_GET['category'] : null;
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'name_asc';

// Pagination parameters
$page = isset($_GET['page']) && is_numeric($_GET['page']) && $_GET['page'] > 0 ? (int)$_GET['page'] : 1;
$products_per_page = 12; // 12 products per page (3 rows x 4 columns)
$offset = ($page - 1) * $products_per_page;

// Get selected category name efficiently
$selectedCategory = null;
if ($category_id) {
    try {
        $catStmt = $conn->prepare("SELECT name FROM categories WHERE category_id = ? LIMIT 1");
        $catStmt->execute([$category_id]);
        $result = $catStmt->fetch(PDO::FETCH_ASSOC);
        $selectedCategory = isset($result['name']) ? $result['name'] : null;
    } catch (PDOException $e) {
        error_log("Error fetching selected category: " . $e->getMessage());
    }
}

// Optimized product fetching using stored procedure or optimized query
$products = [];
try {
    // Use the optimized view for better performance
    $whereClause = "WHERE is_active = 1";
    $params = [];

    if ($category_id) {
        $whereClause .= " AND category_id = ?";
        $params[] = $category_id;
    }

    if (!empty($searchTerm)) {
        $whereClause .= " AND (name LIKE ? OR description LIKE ?)";
        $searchParam = "%{$searchTerm}%";
        $params[] = $searchParam;
        $params[] = $searchParam;
    }

    // Optimized sorting
    $orderClause = match($sort_by) {
        'name_desc' => 'ORDER BY name DESC',
        'price_asc' => 'ORDER BY price ASC',
        'price_desc' => 'ORDER BY price DESC',
        'newest' => 'ORDER BY created_at DESC',
        default => 'ORDER BY name ASC'
    };

    // Count total products for pagination
    $countQuery = "
        SELECT COUNT(*) as total
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        {$whereClause}
    ";

    $countStmt = $conn->prepare($countQuery);
    $countStmt->execute($params);
    $countResult = $countStmt->fetch(PDO::FETCH_ASSOC);
    $total_products = $countResult['total'];

    // Calculate pagination info
    $total_pages = ceil($total_products / $products_per_page);
    $start_product = ($page - 1) * $products_per_page + 1;
    $end_product = min($page * $products_per_page, $total_products);

    // Use a simple, reliable query without reviews for now with pagination
    $productQuery = "
        SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image, p.created_at,
               c.name as category_name,
               0 as rating,
               0 as review_count
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        {$whereClause}
        {$orderClause}
        LIMIT {$products_per_page} OFFSET {$offset}
    ";

    $productStmt = $conn->prepare($productQuery);
    $productStmt->execute($params);
    $products = $productStmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    error_log("Error fetching products: " . $e->getMessage());
    // Set default values for pagination
    $total_products = 0;
    $total_pages = 1;
    $start_product = 0;
    $end_product = 0;

    // Fallback to simple query
    try {
        $simpleQuery = "
            SELECT p.product_id, p.name, p.description, p.price, p.stock, p.image, p.created_at,
                   c.name as category_name, 0 as rating, 0 as review_count
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.category_id
            WHERE p.is_active = 1
            ORDER BY p.name
            LIMIT 20
        ";
        $stmt = $conn->prepare($simpleQuery);
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $total_products = count($products);
        $total_pages = 1;
        $start_product = 1;
        $end_product = count($products);
    } catch (PDOException $e2) {
        $products = [];
        $total_products = 0;
        $total_pages = 1;
        $start_product = 0;
        $end_product = 0;
    }
}

// Optimized cart count using function
$cartCount = 0;
if (isset($_SESSION['user_id'])) {
    try {
        $stmt = $conn->prepare("SELECT GetCartCountFast(?)");
        $stmt->execute([$_SESSION['user_id']]);
        $cartCount = $stmt->fetchColumn() ?: 0;
    } catch (PDOException $e) {
        // Fallback to simple query
        try {
            $stmt = $conn->prepare("SELECT COALESCE(SUM(ci.quantity), 0) FROM cart_items ci JOIN carts c ON ci.cart_id = c.cart_id WHERE c.user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
            $cartCount = $stmt->fetchColumn() ?: 0;
        } catch (PDOException $e2) {
            $cartCount = 0;
        }
    }
}

// Optimized page scripts with lazy loading and performance improvements
$page_scripts = '
<script>
// Optimized script loading with performance monitoring
const perfStart = performance.now();

// Use DOMContentLoaded for faster initialization
document.addEventListener("DOMContentLoaded", function() {
    console.log("🚀 Products page initialized in", Math.round(performance.now() - perfStart), "ms");

    // Optimized notification container creation
    let notificationContainer = null;
    function getNotificationContainer() {
        if (!notificationContainer) {
            notificationContainer = document.createElement("div");
            notificationContainer.id = "notification-container";
            notificationContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                pointer-events: none;
            `;
            document.body.appendChild(notificationContainer);
        }
        return notificationContainer;
    }

    // Show notification
    function showNotification(message, type = "success", duration = 4000) {
        console.log("📢 Showing notification:", message, "Type:", type);

        createNotificationContainer();

        const notification = document.createElement("div");
        notification.className = `alert alert-${type} alert-dismissible fade show notification-toast`;

        // Force visibility with multiple approaches
        notification.style.cssText = `
            margin-bottom: 10px !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
            border: none !important;
            border-radius: 8px !important;
            animation: slideInRight 0.3s ease !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: relative !important;
            z-index: 10000 !important;
            min-height: 50px !important;
            width: 100% !important;
            max-width: 400px !important;
        `;

        const icon = type === "success" ? "check-circle" : type === "danger" ? "exclamation-circle" : "info-circle";
        const iconColor = type === "success" ? "#10b981" : type === "danger" ? "#ef4444" : "#3b82f6";

        // Create notification content with better structure
        const iconElement = document.createElement("i");
        iconElement.className = `fas fa-${icon} me-2`;
        iconElement.style.cssText = `color: ${iconColor}; font-size: 1.1rem; flex-shrink: 0;`;

        const messageElement = document.createElement("div");
        messageElement.className = "flex-grow-1";
        messageElement.style.cssText = "color: #333; font-weight: 500;";
        messageElement.innerHTML = message;

        const closeButton = document.createElement("button");
        closeButton.type = "button";
        closeButton.className = "btn-close";
        closeButton.style.cssText = "flex-shrink: 0;";
        closeButton.onclick = function() {
            notification.remove();
        };

        const notificationContent = document.createElement("div");
        notificationContent.className = "d-flex align-items-center";
        notificationContent.style.cssText = "width: 100%; min-height: 40px; padding: 8px;";

        notificationContent.appendChild(iconElement);
        notificationContent.appendChild(messageElement);
        notificationContent.appendChild(closeButton);

        notification.appendChild(notificationContent);

        const container = document.getElementById("notification-container");
        container.appendChild(notification);

        console.log("📢 Notification added to DOM:", notification);

        // Auto remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = "slideOutRight 0.3s ease";
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);
        }
    }

    // Update cart count in header
    function updateCartCount(count) {
        const cartLink = document.querySelector("a[href=\"cart.php\"]");
        if (cartLink) {
            let cartBadge = cartLink.querySelector(".cart-count");

            if (count > 0) {
                if (!cartBadge) {
                    cartBadge = document.createElement("span");
                    cartBadge.className = "position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-count";
                    cartLink.appendChild(cartBadge);
                }
                cartBadge.textContent = count;
                cartBadge.style.animation = "pulse 0.5s ease";
            } else if (cartBadge) {
                cartBadge.remove();
            }
        }
    }

    // Add to cart function
    function addToCartNow(productId) {
        console.log("🛒 Adding product to cart:", productId);

        if (!productId) {
            showNotification("Invalid product ID", "danger");
            return;
        }

        // Show loading notification
        showNotification("🔄 Adding to cart...", "info", 0);

        // Get reference to the loading notification for removal
        const loadingNotification = document.querySelector("#notification-container .alert-info:last-child");



        // Disable the button temporarily
        const button = document.querySelector(`button[data-product-id="${productId}"]`);
        if (button) {
            button.disabled = true;
            button.innerHTML = "<i class=\"fas fa-spinner fa-spin me-2\"></i>Adding...";
        }

        // Send AJAX request with better error handling
        fetch("ajax/add_to_cart_simple.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: `product_id=${productId}&quantity=1`
        })
        .then(response => {
            console.log("📡 Response status:", response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // Get as text first to debug
        })
        .then(text => {
            console.log("📡 Raw response:", text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error("❌ JSON parse error:", e);
                console.error("❌ Response text:", text);
                throw new Error("Invalid JSON response from server");
            }
        })
        .then(data => {
            console.log("📡 Parsed data:", data);
            // Remove loading notification
            if (loadingNotification.parentElement) {
                loadingNotification.remove();
            }

            if (data.success) {
                showNotification(
                    `✅ <strong>${data.data.product_name || "Product"}</strong> berhasil ditambahkan ke keranjang!<br>
                    <small>Keranjang sekarang memiliki ${data.data.cart_count} item</small>`,
                    "success"
                );

                // Update cart count
                updateCartCount(data.data.cart_count);

                // Reset button
                if (button) {
                    button.disabled = false;
                    button.innerHTML = "<i class=\"fas fa-cart-plus me-2\"></i>Add to Cart";

                    // Add success animation
                    button.style.background = "#10b981";
                    setTimeout(() => {
                        button.style.background = "";
                    }, 1000);
                }
            } else {
                showNotification("❌ " + (data.message || "Gagal menambahkan produk ke keranjang"), "danger");

                // Reset button
                if (button) {
                    button.disabled = false;
                    button.innerHTML = "<i class=\"fas fa-cart-plus me-2\"></i>Add to Cart";
                }
            }
        })
        .catch(error => {
            console.error("Error:", error);

            // Remove loading notification
            if (loadingNotification.parentElement) {
                loadingNotification.remove();
            }

            showNotification("Network error. Please try again.", "danger");

            // Reset button
            if (button) {
                button.disabled = false;
                button.innerHTML = "<i class=\"fas fa-cart-plus me-2\"></i>Add to Cart";
            }
        });
    }

    // Make function global
    window.addToCartNow = addToCartNow;

    // Add CSS animations
    const style = document.createElement("style");
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .notification-toast {
            animation: slideInRight 0.3s ease;
        }
    `;
    document.head.appendChild(style);

    console.log("✅ Add to cart function ready!");
    console.log("✅ Function type:", typeof addToCartNow);
    console.log("✅ Window function type:", typeof window.addToCartNow);
});
</script>
';

include 'includes/header.php';
?>

<style>
/* Products Page Styles */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1d4ed8;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --white: #ffffff;
    
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.1);
    --shadow-lg: 0 8px 30px rgba(0,0,0,0.15);
    --radius-lg: 20px;
    --radius-md: 12px;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, sans-serif;
    background-color: var(--light-color);
}

.products-header {
    background: var(--primary-gradient);
    color: white;
    padding: 60px 0 40px;
    margin-bottom: 40px;
}

.products-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.products-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.filters-section {
    background: white;
    border-radius: var(--radius-lg);
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-md);
}

.filter-group {
    margin-bottom: 20px;
}

.filter-label {
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.category-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.category-btn {
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    background: white;
    color: var(--dark-color);
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.category-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    text-decoration: none;
}

.category-btn.active {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    color: white;
}

.category-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.category-btn .badge {
    font-size: 0.7rem;
    padding: 2px 6px;
}

.results-summary {
    background: white;
    border-radius: var(--radius-md);
    padding: 20px;
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
}

.results-summary h5 {
    color: var(--dark-color);
    font-weight: 600;
}

.breadcrumb {
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-md);
    padding: 12px 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.breadcrumb-item a {
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: var(--secondary-color);
}

/* Pagination Styles */
.pagination-wrapper {
    margin-top: 40px;
    padding: 30px 0;
}

.pagination .page-link {
    border: 2px solid #e2e8f0;
    color: var(--dark-color);
    padding: 12px 16px;
    margin: 0 4px;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
}

.pagination .page-link:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: var(--primary-gradient);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.pagination .page-item.disabled .page-link {
    color: #9ca3af;
    border-color: #e5e7eb;
    background-color: #f9fafb;
    cursor: not-allowed;
}

.pagination .page-item.disabled .page-link:hover {
    transform: none;
    background-color: #f9fafb;
    border-color: #e5e7eb;
}

/* Products per page info */
.pagination-info {
    background: white;
    border-radius: var(--radius-md);
    padding: 15px 20px;
    margin-top: 20px;
    box-shadow: var(--shadow-md);
    text-align: center;
}

.search-sort-row {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    flex: 1;
    min-width: 250px;
}

.search-input {
    border: 2px solid #e2e8f0;
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    outline: none;
}

.sort-select {
    border: 2px solid #e2e8f0;
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-size: 1rem;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.product-card {
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-category {
    color: #6b7280;
    font-size: 0.85rem;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
    line-height: 1.4;
}

.product-rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.rating-stars {
    color: var(--warning-color);
    font-size: 0.9rem;
}

.rating-text {
    color: #6b7280;
    font-size: 0.85rem;
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 15px;
    margin-top: auto;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn-add-to-cart {
    flex: 1;
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-md);
    padding: 12px 16px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    color: white;
}

.btn-view-detail {
    background: white;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    border-radius: var(--radius-md);
    padding: 12px 16px;
    transition: all 0.3s ease;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.btn-view-detail:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

.no-products {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.no-products i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #d1d5db;
}

/* Responsive */
@media (max-width: 768px) {
    .products-title {
        font-size: 2rem;
    }
    
    .search-sort-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .category-filter {
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}
</style>

<!-- Products Header -->
<section class="products-header">
    <div class="container">
        <div class="text-center">
            <h1 class="products-title">
                <?php if ($selectedCategory): ?>
                    <?php echo htmlspecialchars($selectedCategory); ?>
                <?php elseif ($searchTerm): ?>
                    Search Results for "<?php echo htmlspecialchars($searchTerm); ?>"
                <?php else: ?>
                    All Products
                <?php endif; ?>
            </h1>
            <p class="products-subtitle">
                <?php if ($selectedCategory): ?>
                    Explore our <?php echo htmlspecialchars($selectedCategory); ?> collection
                <?php elseif ($searchTerm): ?>
                    Found <?php echo count($products); ?> products matching your search
                <?php else: ?>
                    Discover amazing products at unbeatable prices
                <?php endif; ?>
            </p>
        </div>
    </div>
</section>

<!-- Breadcrumb Navigation -->
<?php if ($selectedCategory || $searchTerm): ?>
<div class="container">
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="index.php" class="text-decoration-none">
                    <i class="fas fa-home me-1"></i>Home
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="products_public.php" class="text-decoration-none">Products</a>
            </li>
            <?php if ($selectedCategory): ?>
                <li class="breadcrumb-item active" aria-current="page">
                    <?php echo htmlspecialchars($selectedCategory); ?>
                </li>
            <?php endif; ?>
            <?php if ($searchTerm): ?>
                <li class="breadcrumb-item active" aria-current="page">
                    Search: "<?php echo htmlspecialchars($searchTerm); ?>"
                </li>
            <?php endif; ?>
        </ol>
    </nav>
</div>
<?php endif; ?>

<!-- Filters and Search -->
<div class="container">
    <div class="filters-section">
        <!-- Category Filter -->
        <div class="filter-group">
            <div class="filter-label">
                <i class="fas fa-tags me-2"></i>Categories
                <?php if ($selectedCategory): ?>
                    <span class="badge bg-primary ms-2"><?php echo htmlspecialchars($selectedCategory); ?></span>
                <?php endif; ?>
            </div>
            <div class="category-filter">
                <?php
                // Calculate total products for "All Products" button
                $totalProductsCount = 0;
                if (!empty($categories)) {
                    foreach ($categories as $cat) {
                        $totalProductsCount += isset($cat['product_count']) ? $cat['product_count'] : 0;
                    }
                }
                ?>
                <a href="products_public.php" class="category-btn <?php echo !isset($_GET['category']) ? 'active' : ''; ?>">
                    <i class="fas fa-th-large me-1"></i>All Products
                    <?php if ($totalProductsCount > 0): ?>
                        <span class="badge bg-light text-dark ms-1"><?php echo $totalProductsCount; ?></span>
                    <?php endif; ?>
                </a>
                <?php if (!empty($categories)): ?>
                    <?php foreach ($categories as $category): ?>
                        <?php if (isset($category['category_id']) && isset($category['name'])): ?>
                            <?php
                            $isActive = (isset($_GET['category']) && $_GET['category'] == $category['category_id']);
                            $productCount = isset($category['product_count']) ? $category['product_count'] : 0;

                            // Set category icon based on category name
                            $categoryIcon = 'fas fa-cube'; // default
                            switch($category['name']) {
                                case 'Cosmetics': $categoryIcon = 'fas fa-palette'; break;
                                case 'Medicine': $categoryIcon = 'fas fa-pills'; break;
                                case 'Milk Products': $categoryIcon = 'fas fa-glass-whiskey'; break;
                                case 'Sports': $categoryIcon = 'fas fa-dumbbell'; break;
                                case 'Vegetables': $categoryIcon = 'fas fa-carrot'; break;
                                case 'Electronics': $categoryIcon = 'fas fa-laptop'; break;
                                case 'Fashion': $categoryIcon = 'fas fa-tshirt'; break;
                                case 'Home & Garden': $categoryIcon = 'fas fa-home'; break;
                                case 'Books': $categoryIcon = 'fas fa-book'; break;
                            }
                            ?>
                            <a href="products_public.php?category=<?php echo htmlspecialchars($category['category_id']); ?><?php echo isset($_GET['search']) ? '&search=' . urlencode($_GET['search']) : ''; ?><?php echo isset($_GET['sort']) ? '&sort=' . urlencode($_GET['sort']) : ''; ?>"
                               class="category-btn <?php echo $isActive ? 'active' : ''; ?> <?php echo $productCount == 0 ? 'disabled' : ''; ?>"
                               title="<?php echo htmlspecialchars(isset($category['description']) ? $category['description'] : $category['name']); ?> (<?php echo $productCount; ?> products)">
                                <i class="<?php echo $categoryIcon; ?> me-1"></i>
                                <?php echo htmlspecialchars($category['name']); ?>
                                <?php if ($productCount > 0): ?>
                                    <span class="badge bg-light text-dark ms-1"><?php echo $productCount; ?></span>
                                <?php else: ?>
                                    <span class="badge bg-secondary ms-1">0</span>
                                <?php endif; ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center text-muted py-3">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>No categories available</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Search and Sort -->
        <div class="search-sort-row">
            <div class="search-box">
                <form method="GET" action="products_public.php">
                    <?php if (isset($_GET['category'])): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($_GET['category']); ?>">
                    <?php endif; ?>
                    <?php if (isset($_GET['sort'])): ?>
                        <input type="hidden" name="sort" value="<?php echo htmlspecialchars($_GET['sort']); ?>">
                    <?php endif; ?>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control search-input"
                               placeholder="Search products..."
                               value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <div class="sort-box">
                <form method="GET" action="products_public.php" id="sortForm">
                    <?php if (isset($_GET['category'])): ?>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($_GET['category']); ?>">
                    <?php endif; ?>
                    <?php if (isset($_GET['search'])): ?>
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($_GET['search']); ?>">
                    <?php endif; ?>
                    <select name="sort" class="form-select sort-select" onchange="document.getElementById('sortForm').submit();">
                        <option value="name_asc" <?php echo $sort_by == 'name_asc' ? 'selected' : ''; ?>>Name A-Z</option>
                        <option value="name_desc" <?php echo $sort_by == 'name_desc' ? 'selected' : ''; ?>>Name Z-A</option>
                        <option value="price_asc" <?php echo $sort_by == 'price_asc' ? 'selected' : ''; ?>>Price Low to High</option>
                        <option value="price_desc" <?php echo $sort_by == 'price_desc' ? 'selected' : ''; ?>>Price High to Low</option>
                        <option value="newest" <?php echo $sort_by == 'newest' ? 'selected' : ''; ?>>Newest First</option>
                    </select>
                </form>
            </div>
        </div>
    </div>

    <!-- Results Summary -->
    <div class="results-summary mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-0">
                    <?php if ($selectedCategory): ?>
                        <i class="fas fa-filter me-2 text-primary"></i>
                        "<?php echo htmlspecialchars($selectedCategory); ?>" Category
                    <?php elseif ($searchTerm): ?>
                        <i class="fas fa-search me-2 text-primary"></i>
                        Search Results for "<?php echo htmlspecialchars($searchTerm); ?>"
                    <?php else: ?>
                        <i class="fas fa-cube me-2 text-primary"></i>
                        All Products
                    <?php endif; ?>
                </h5>
                <p class="mb-0 text-muted">
                    <?php if ($total_products > 0): ?>
                        Showing <?php echo $start_product; ?>-<?php echo $end_product; ?> of <?php echo $total_products; ?> products
                        <?php if ($total_pages > 1): ?>
                            (Page <?php echo $page; ?> of <?php echo $total_pages; ?>)
                        <?php endif; ?>
                    <?php else: ?>
                        No products found
                    <?php endif; ?>
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <?php if ($selectedCategory || $searchTerm): ?>
                    <a href="products_public.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-1"></i>Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Products Grid -->
    <?php if (!empty($products)): ?>
        <div class="products-grid">
            <?php foreach ($products as $product): ?>
                <div class="product-card">
                    <div class="product-image">
                        <?php if ((isset($product['stock']) ? $product['stock'] : 0) <= 0): ?>
                            <div class="product-badge bg-secondary">Out of Stock</div>
                        <?php elseif (isset($product['created_at']) && strtotime($product['created_at']) > strtotime('-7 days')): ?>
                            <div class="product-badge bg-success">New</div>
                        <?php endif; ?>

                        <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                             alt="<?php echo htmlspecialchars(isset($product['name']) ? $product['name'] : 'Product'); ?>"
                             class="product-img">
                    </div>

                    <div class="product-info">
                        <div class="product-category"><?php echo htmlspecialchars(isset($product['category_name']) ? $product['category_name'] : 'General'); ?></div>
                        <h6 class="product-title"><?php echo htmlspecialchars(isset($product['name']) ? $product['name'] : 'Product Name'); ?></h6>

                        <div class="product-rating">
                            <div class="rating-stars">
                                <?php
                                $rating = round(isset($product['rating']) ? $product['rating'] : 0);
                                for ($i = 1; $i <= 5; $i++) {
                                    echo $i <= $rating ? '<i class="fas fa-star"></i>' : '<i class="far fa-star"></i>';
                                }
                                ?>
                            </div>
                            <span class="rating-text">(<?php echo isset($product['review_count']) ? $product['review_count'] : 0; ?>)</span>
                        </div>

                        <div class="product-price">
                            Rp <?php echo number_format(isset($product['price']) ? $product['price'] : 0, 0, ',', '.'); ?>
                        </div>

                        <div class="product-actions">
                            <?php if ((isset($product['stock']) ? $product['stock'] : 0) > 0): ?>
                                <?php if (isset($_SESSION['user_id'])): ?>
                                    <div class="product-card" data-product-id="<?php echo isset($product['product_id']) ? $product['product_id'] : 0; ?>">
                                        <button class="btn btn-add-to-cart add-to-cart-btn"
                                                data-product-id="<?php echo isset($product['product_id']) ? $product['product_id'] : 0; ?>">
                                            <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                        </button>
                                    </div>
                                <?php else: ?>
                                    <a href="login.php" class="btn btn-add-to-cart">
                                        <i class="fas fa-sign-in-alt me-2"></i>Login to Buy
                                    </a>
                                <?php endif; ?>
                                <a href="product-detail.php?id=<?php echo isset($product['product_id']) ? $product['product_id'] : 0; ?>" class="btn-view-detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                            <?php else: ?>
                                <button class="btn btn-secondary" disabled>
                                    <i class="fas fa-times me-2"></i>Out of Stock
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination-wrapper mt-5">
                <nav aria-label="Products pagination">
                    <ul class="pagination justify-content-center">
                        <!-- Previous Page -->
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl($page - 1, $category_id, $searchTerm, $sort_by); ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <span class="page-link">&laquo;</span>
                            </li>
                        <?php endif; ?>

                        <!-- Page Numbers -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        // Show first page if not in range
                        if ($start_page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl(1, $category_id, $searchTerm, $sort_by); ?>">1</a>
                            </li>
                            <?php if ($start_page > 2): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Current page range -->
                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="<?php echo buildPaginationUrl($i, $category_id, $searchTerm, $sort_by); ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>

                        <!-- Show last page if not in range -->
                        <?php if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl($total_pages, $category_id, $searchTerm, $sort_by); ?>"><?php echo $total_pages; ?></a>
                            </li>
                        <?php endif; ?>

                        <!-- Next Page -->
                        <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="<?php echo buildPaginationUrl($page + 1, $category_id, $searchTerm, $sort_by); ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        <?php else: ?>
                            <li class="page-item disabled">
                                <span class="page-link">&raquo;</span>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>

                <!-- Page Info -->
                <div class="text-center mt-3">
                    <small class="text-muted">
                        Page <?php echo $page; ?> of <?php echo $total_pages; ?>
                        (<?php echo $total_products; ?> total products)
                    </small>
                </div>
            </div>
        <?php endif; ?>

    <?php else: ?>
        <div class="no-products">
            <i class="fas fa-box-open"></i>
            <h4>No Products Found</h4>
            <p>
                <?php if ($selectedCategory): ?>
                    No products available in this category at the moment.
                <?php elseif ($searchTerm): ?>
                    No products match your search criteria. Try different keywords.
                <?php else: ?>
                    No products available at the moment. Please check back later.
                <?php endif; ?>
            </p>
            <a href="products_public.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>View All Products
            </a>
        </div>
    <?php endif; ?>
</div>



<!-- Load Simple Cart JavaScript -->
<script src="assets/js/simple-cart.js"></script>

<?php include 'includes/footer.php'; ?>
