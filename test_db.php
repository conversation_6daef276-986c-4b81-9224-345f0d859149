<?php
echo "<h2>🔍 Database Connection Test</h2>";

// Test 1: Direct connection
echo "<h3>Test 1: Direct PDO Connection</h3>";
try {
    $conn = new PDO("mysql:host=localhost;dbname=db_tewuneed;charset=utf8mb4", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div style='color: green;'>✅ Direct connection successful</div>";
    
    // Test query
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products");
    $stmt->execute();
    $result = $stmt->fetch();
    echo "<div>Products count: " . $result['count'] . "</div>";
    
    // Get first few products
    $stmt = $conn->prepare("SELECT product_id, NAME FROM products LIMIT 5");
    $stmt->execute();
    $products = $stmt->fetchAll();
    
    echo "<h4>First 5 products:</h4>";
    foreach ($products as $product) {
        echo "<div>ID: {$product['product_id']} - {$product['NAME']}</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Direct connection failed: " . $e->getMessage() . "</div>";
}

// Test 2: Using includes
echo "<h3>Test 2: Using includes/db_connect.php</h3>";
try {
    require_once 'includes/db_connect.php';
    echo "<div style='color: green;'>✅ Include successful</div>";
    
    if (isset($conn)) {
        echo "<div style='color: green;'>✅ Connection variable exists</div>";
        
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM products");
        $stmt->execute();
        $result = $stmt->fetch();
        echo "<div>Products count via include: " . $result['count'] . "</div>";
    } else {
        echo "<div style='color: red;'>❌ Connection variable not set</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Include failed: " . $e->getMessage() . "</div>";
}

// Test 3: Check if database exists
echo "<h3>Test 3: Database Existence Check</h3>";
try {
    $conn = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "");
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $conn->prepare("SHOW DATABASES LIKE 'db_tewuneed'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo "<div style='color: green;'>✅ Database 'db_tewuneed' exists</div>";
    } else {
        echo "<div style='color: red;'>❌ Database 'db_tewuneed' does not exist</div>";
        
        // Show available databases
        $stmt = $conn->prepare("SHOW DATABASES");
        $stmt->execute();
        $databases = $stmt->fetchAll();
        
        echo "<h4>Available databases:</h4>";
        foreach ($databases as $db) {
            echo "<div>- " . $db['Database'] . "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database check failed: " . $e->getMessage() . "</div>";
}
?>
