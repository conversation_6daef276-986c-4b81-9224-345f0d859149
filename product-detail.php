<?php
session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
require_once 'includes/ReviewManager.php';

// Get product ID from URL
$product_id = $_GET['id'] ?? null;

if (!$product_id) {
    header('Location: products.php');
    exit;
}

// Get product details
try {
    $stmt = $conn->prepare("
        SELECT p.*, c.NAME as category_name, c.slug as category_slug
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE p.product_id = ?
    ");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$product) {
        throw new Exception('Product not found');
    }

} catch (Exception $e) {
    $_SESSION['error'] = $e->getMessage();
    header('Location: products.php');
    exit;
}

// Get product reviews and rating summary
$reviewManager = new ReviewManager();
$rating_summary = $reviewManager->getProductRatingSummary($product_id);
$recent_reviews = $reviewManager->getProductReviews($product_id, 1, 3); // Get 3 most recent reviews

// Ensure rating_summary has default values if null
if (!$rating_summary) {
    $rating_summary = [
        'average_rating' => 0,
        'total_reviews' => 0,
        'rating_distribution' => [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0]
    ];
}

// Ensure product has all required keys with defaults
$product = array_merge([
    'NAME' => 'Unknown Product',
    'description' => '',
    'price' => 0,
    'stock' => 0,
    'image' => '',
    'category_name' => 'General'
], $product);

// Use NAME field for display (database uses NAME not name)
$product['name'] = $product['NAME'];

$page = 'product-detail';
$page_title = $product['name'] . ' - TeWuNeed';
require_once 'includes/header.php';
?>

<!-- Product Detail -->
<div class="container py-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="products.php">Products</a></li>
            <li class="breadcrumb-item">
                <a href="products.php?category=<?php echo urlencode($product['category_slug'] ?? $product['category_name']); ?>">
                    <?php echo htmlspecialchars($product['category_name']); ?>
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                <?php echo htmlspecialchars($product['name']); ?>
            </li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Image -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <img src="<?php echo !empty($product['image']) ? 'uploads/' . htmlspecialchars($product['image']) : 'Images/default-product.jpg'; ?>"
                     class="card-img-top"
                     alt="<?php echo htmlspecialchars($product['name']); ?>"
                     onerror="this.src='Images/default-product.jpg'">
            </div>
        </div>

        <!-- Product Info -->
        <div class="col-md-6">
            <h1 class="mb-3"><?php echo htmlspecialchars($product['name']); ?></h1>

            <div class="mb-3">
                <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category_name']); ?></span>
                <?php if ($product['stock'] > 0): ?>
                    <span class="badge bg-success">In Stock</span>
                <?php else: ?>
                    <span class="badge bg-danger">Out of Stock</span>
                <?php endif; ?>
            </div>

            <h2 class="h4 mb-3">Rp <?php echo number_format($product['price']); ?></h2>

            <!-- Product Rating -->
            <?php if (isset($rating_summary['total_reviews']) && $rating_summary['total_reviews'] > 0): ?>
            <div class="mb-3">
                <div class="d-flex align-items-center">
                    <?php echo ReviewManager::generateStarRating($rating_summary['average_rating'] ?? 0); ?>
                    <span class="ms-2 text-muted">
                        (<?php echo number_format($rating_summary['total_reviews'] ?? 0); ?> reviews)
                    </span>
                    <a href="product_reviews.php?product_id=<?php echo $product_id; ?>"
                       class="ms-2 text-decoration-none">
                        See all reviews
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <div class="mb-4">
                <p><?php echo nl2br(htmlspecialchars($product['description'])); ?></p>
            </div>

            <?php if ($product['stock'] > 0): ?>
            <div class="mb-4 product-detail" data-product-id="<?php echo $product['product_id']; ?>">
                <label class="form-label fw-bold">Quantity:</label>
                <div class="input-group quantity-selector" style="max-width: 200px;">
                    <button class="btn btn-outline-secondary decrease btn-decrease"
                            type="button"
                            id="decrease-quantity"
                            data-action="decrease">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number"
                           id="quantity"
                           class="form-control text-center qty-input quantity-input"
                           value="1"
                           min="1"
                           max="<?php echo $product['stock']; ?>"
                           data-stock="<?php echo $product['stock']; ?>">
                    <button class="btn btn-outline-secondary increase btn-increase"
                            type="button"
                            id="increase-quantity"
                            data-action="increase">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                <small class="text-muted">Available: <span class="fw-bold text-success"><?php echo $product['stock']; ?></span> units</small>
            </div>

            <div class="d-flex gap-2 flex-wrap">
                <button class="btn btn-primary btn-lg add-to-cart-btn btn-add-to-cart"
                        data-product-id="<?php echo $product['product_id']; ?>"
                        data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                        data-product-price="<?php echo $product['price']; ?>"
                        data-stock="<?php echo $product['stock']; ?>">
                    <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                </button>

                <button class="btn btn-outline-primary btn-lg wishlist-btn"
                        data-product-id="<?php echo $product['product_id']; ?>"
                        title="Add to Wishlist">
                    <i class="fas fa-heart me-2"></i>Wishlist
                </button>

                <button class="btn btn-outline-secondary btn-lg share-btn"
                        data-product-id="<?php echo $product['product_id']; ?>"
                        data-product-name="<?php echo htmlspecialchars($product['name']); ?>"
                        title="Share Product">
                    <i class="fas fa-share-alt me-2"></i>Share
                </button>
            </div>
            <?php else: ?>
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Out of Stock</strong> - This product is currently unavailable
            </div>
            <button class="btn btn-secondary btn-lg" disabled>
                <i class="fas fa-times me-2"></i>Out of Stock
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Reviews Section -->
    <?php if (!empty($recent_reviews) || (isset($rating_summary['total_reviews']) && $rating_summary['total_reviews'] > 0)): ?>
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-star text-warning me-2"></i>Customer Reviews
                    </h4>
                    <a href="product_reviews.php?product_id=<?php echo $product_id; ?>"
                       class="btn btn-outline-primary btn-sm">
                        View All Reviews (<?php echo $rating_summary['total_reviews'] ?? 0; ?>)
                    </a>
                </div>
                <div class="card-body">
                    <?php if (isset($rating_summary['total_reviews']) && $rating_summary['total_reviews'] > 0): ?>
                    <!-- Rating Summary -->
                    <div class="row mb-4">
                        <div class="col-md-4 text-center">
                            <div class="display-6 fw-bold text-primary">
                                <?php echo number_format($rating_summary['average_rating'] ?? 0, 1); ?>
                            </div>
                            <div class="mb-2">
                                <?php echo ReviewManager::generateStarRating($rating_summary['average_rating'] ?? 0, false); ?>
                            </div>
                            <p class="text-muted mb-0">
                                Based on <?php echo number_format($rating_summary['total_reviews'] ?? 0); ?> reviews
                            </p>
                        </div>
                        <div class="col-md-8">
                            <!-- Rating Breakdown -->
                            <?php for ($i = 5; $i >= 1; $i--): ?>
                                <?php
                                $count = $rating_summary["rating_{$i}_count"] ?? 0;
                                $total_reviews = $rating_summary['total_reviews'] ?? 0;
                                $percentage = $total_reviews > 0 ? ($count / $total_reviews) * 100 : 0;
                                ?>
                                <div class="d-flex align-items-center mb-1">
                                    <div class="me-2" style="width: 60px;">
                                        <?php echo $i; ?> <i class="fas fa-star text-warning"></i>
                                    </div>
                                    <div class="progress flex-grow-1 me-2" style="height: 6px;">
                                        <div class="progress-bar bg-warning"
                                             style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                    <div style="width: 40px;">
                                        <?php echo $count; ?>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Recent Reviews -->
                    <?php if (!empty($recent_reviews)): ?>
                    <h6 class="mb-3">Recent Reviews:</h6>
                    <div class="row">
                        <?php foreach ($recent_reviews as $review): ?>
                        <div class="col-md-4 mb-3">
                            <div class="border rounded p-3 h-100">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <small class="fw-bold"><?php echo htmlspecialchars($review['user_name']); ?></small>
                                    <?php echo ReviewManager::generateStarRating($review['rating'], false); ?>
                                </div>
                                <?php if ($review['review_title']): ?>
                                    <h6 class="small mb-1"><?php echo htmlspecialchars($review['review_title']); ?></h6>
                                <?php endif; ?>
                                <p class="small text-muted mb-2">
                                    <?php echo htmlspecialchars(substr($review['review_text'], 0, 100)); ?>
                                    <?php if (strlen($review['review_text']) > 100): ?>...<?php endif; ?>
                                </p>
                                <small class="text-muted">
                                    <?php echo date('M d, Y', strtotime($review['created_at'])); ?>
                                </small>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>

                    <!-- Write Review Button -->
                    <div class="text-center mt-3">
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <a href="write_review.php?product_id=<?php echo $product_id; ?>"
                               class="btn btn-success">
                                <i class="fas fa-edit me-2"></i>Write a Review
                            </a>
                        <?php else: ?>
                            <a href="login.php" class="btn btn-outline-success">
                                <i class="fas fa-sign-in-alt me-2"></i>Login to Write Review
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Alert Container -->
<div id="alert-container" class="position-fixed top-0 end-0 p-3"></div>

<!-- Loading Spinner -->
<div id="spinner" class="position-fixed top-50 start-50 translate-middle" style="display: none; z-index: 1060">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<?php
$page_specific_js = [
    'assets/js/universal-product-handler.js',
    'assets/js/simple-cart.js',
    'assets/js/product-detail.js'
];
require_once 'includes/footer.php';
?>
