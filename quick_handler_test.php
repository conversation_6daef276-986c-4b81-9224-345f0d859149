<?php
/**
 * Quick Handler Test - Verify Enhanced Product Detail Handler Loading
 */
session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Get a sample product
try {
    $stmt = $conn->prepare("SELECT * FROM products WHERE stock > 0 LIMIT 1");
    $stmt->execute();
    $test_product = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $test_product = null;
}

$product_name = $test_product['NAME'] ?? $test_product['name'] ?? 'Test Product';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Handler Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <h1>🧪 Quick Handler Test</h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Testing Enhanced Product Detail Handler</h5>
            <p>This page tests if the enhanced handler loads correctly with minimal setup.</p>
        </div>
        
        <!-- Minimal Product Detail Structure -->
        <div class="card">
            <div class="card-body">
                <h5 class="card-title"><?php echo htmlspecialchars($product_name); ?></h5>
                
                <?php if ($test_product): ?>
                <div class="product-detail" data-product-id="<?php echo $test_product['product_id']; ?>">
                    <!-- Quantity Controls -->
                    <div class="mb-3">
                        <label class="form-label">Quantity:</label>
                        <div class="input-group" style="max-width: 200px;">
                            <button class="btn btn-outline-secondary decrease btn-decrease" type="button" id="decrease-quantity">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" id="quantity" class="form-control text-center qty-input quantity-input" 
                                   value="1" min="1" max="<?php echo $test_product['stock']; ?>">
                            <button class="btn btn-outline-secondary increase btn-increase" type="button" id="increase-quantity">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary add-to-cart-btn btn-add-to-cart" 
                                data-product-id="<?php echo $test_product['product_id']; ?>"
                                data-product-name="<?php echo htmlspecialchars($product_name); ?>"
                                data-product-price="<?php echo $test_product['price']; ?>">
                            <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                        </button>
                        
                        <button class="btn btn-outline-primary wishlist-btn" 
                                data-product-id="<?php echo $test_product['product_id']; ?>">
                            <i class="fas fa-heart me-2"></i>Wishlist
                        </button>
                        
                        <button class="btn btn-outline-secondary share-btn" 
                                data-product-id="<?php echo $test_product['product_id']; ?>"
                                data-product-name="<?php echo htmlspecialchars($product_name); ?>">
                            <i class="fas fa-share-alt me-2"></i>Share
                        </button>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-warning">No test product available</div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Status Display -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-line me-2"></i>Handler Status</h5>
            </div>
            <div class="card-body">
                <div id="status-display">
                    <div class="text-muted">
                        <i class="fas fa-spinner fa-spin me-2"></i>Checking handler status...
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="checkHandlerStatus()">
                        <i class="fas fa-refresh me-2"></i>Refresh Status
                    </button>
                    <button class="btn btn-success" onclick="testHandler()">
                        <i class="fas fa-play me-2"></i>Test Handler
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="mt-4">
            <a href="product-detail.php?id=<?php echo $test_product['product_id'] ?? 400; ?>" class="btn btn-info">
                <i class="fas fa-arrow-right me-2"></i>Go to Full Product Detail
            </a>
            <a href="test_product_detail_buttons.php" class="btn btn-secondary">
                <i class="fas fa-vial me-2"></i>Full Test Suite
            </a>
        </div>
    </div>
    
    <!-- Alert Container -->
    <div id="alert-container" class="position-fixed top-0 end-0 p-3"></div>
    
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/universal-product-handler.js"></script>
    <script src="assets/js/simple-cart.js"></script>
    <script src="assets/js/product-detail.js"></script>
    
    <script>
    function checkHandlerStatus() {
        const statusDiv = document.getElementById('status-display');
        
        let status = {
            handlerExists: !!window.productDetailHandler,
            handlerReady: !!(window.productDetailHandler && window.productDetailHandler.getStatus),
            classExists: typeof EnhancedProductDetailHandler !== 'undefined',
            elementsFound: {
                quantityInput: !!document.getElementById('quantity'),
                decreaseBtn: !!document.getElementById('decrease-quantity'),
                increaseBtn: !!document.getElementById('increase-quantity'),
                addToCartBtn: !!document.querySelector('.add-to-cart-btn'),
                wishlistBtn: !!document.querySelector('.wishlist-btn'),
                shareBtn: !!document.querySelector('.share-btn')
            }
        };
        
        if (window.productDetailHandler && window.productDetailHandler.getStatus) {
            try {
                status.handlerStatus = window.productDetailHandler.getStatus();
            } catch (error) {
                status.handlerError = error.message;
            }
        }
        
        let html = '<div class="row">';
        
        // Basic Status
        html += '<div class="col-md-6">';
        html += '<h6>Basic Status</h6>';
        html += `<div class="mb-2">Handler Exists: <span class="badge ${status.handlerExists ? 'bg-success' : 'bg-danger'}">${status.handlerExists ? 'YES' : 'NO'}</span></div>`;
        html += `<div class="mb-2">Handler Ready: <span class="badge ${status.handlerReady ? 'bg-success' : 'bg-danger'}">${status.handlerReady ? 'YES' : 'NO'}</span></div>`;
        html += `<div class="mb-2">Class Exists: <span class="badge ${status.classExists ? 'bg-success' : 'bg-danger'}">${status.classExists ? 'YES' : 'NO'}</span></div>`;
        html += '</div>';
        
        // Elements Status
        html += '<div class="col-md-6">';
        html += '<h6>Elements Found</h6>';
        Object.entries(status.elementsFound).forEach(([key, value]) => {
            html += `<div class="mb-2">${key}: <span class="badge ${value ? 'bg-success' : 'bg-warning'}">${value ? 'YES' : 'NO'}</span></div>`;
        });
        html += '</div>';
        
        html += '</div>';
        
        if (status.handlerStatus) {
            html += '<hr><h6>Handler Details</h6>';
            html += `<div class="mb-2">Ready: <span class="badge ${status.handlerStatus.ready ? 'bg-success' : 'bg-warning'}">${status.handlerStatus.ready ? 'YES' : 'NO'}</span></div>`;
        }
        
        if (status.handlerError) {
            html += `<div class="alert alert-danger mt-3">Error: ${status.handlerError}</div>`;
        }
        
        statusDiv.innerHTML = html;
    }
    
    function testHandler() {
        if (!window.productDetailHandler) {
            alert('❌ Handler not available');
            return;
        }
        
        try {
            const results = window.productDetailHandler.testAllFunctionality();
            const passCount = results.filter(r => r.result).length;
            
            alert(`🧪 Test Results: ${passCount}/${results.length} tests passed`);
        } catch (error) {
            alert(`❌ Test failed: ${error.message}`);
        }
    }
    
    // Auto-check status when page loads
    setTimeout(checkHandlerStatus, 1000);
    
    // Check again after 3 seconds
    setTimeout(checkHandlerStatus, 3000);
    </script>
</body>
</html>
