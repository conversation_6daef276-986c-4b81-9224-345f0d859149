<?php
/**
 * Test Universal Product Handler
 * Comprehensive test page for all product detail functionality
 */

session_start();
require_once 'includes/db_connect.php';

$page_title = 'Universal Product Handler Test';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">🧪 Universal Product Handler Test</h1>
                
                <!-- Status Check -->
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle me-2"></i>System Status</h5>
                    <div id="status-check">
                        <p>🔄 Checking system status...</p>
                    </div>
                </div>

                <!-- Login Status -->
                <div class="alert <?php echo isset($_SESSION['user_id']) ? 'alert-success' : 'alert-warning'; ?>">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <i class="fas fa-check-circle me-2"></i>✅ User is logged in (ID: <?php echo $_SESSION['user_id']; ?>)
                    <?php else: ?>
                        <i class="fas fa-exclamation-triangle me-2"></i>⚠️ User not logged in. <a href="login.php">Login</a> to test add to cart functionality.
                    <?php endif; ?>
                </div>

                <!-- Test Product Cards -->
                <div class="row">
                    <?php
                    // Get sample products for testing
                    try {
                        $stmt = $conn->prepare("SELECT product_id, NAME, price, stock, image FROM products WHERE stock > 0 LIMIT 6");
                        $stmt->execute();
                        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        foreach ($products as $product):
                    ?>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100" data-product-id="<?php echo $product['product_id']; ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($product['NAME']); ?></h5>
                                <p class="card-text">
                                    <strong>Price:</strong> Rp <?php echo number_format($product['price']); ?><br>
                                    <strong>Stock:</strong> <?php echo $product['stock']; ?> units
                                </p>
                                
                                <!-- Quantity Controls -->
                                <div class="mb-3">
                                    <label class="form-label">Quantity:</label>
                                    <div class="input-group quantity-selector" style="max-width: 150px;">
                                        <button class="btn btn-outline-secondary decrease" type="button">-</button>
                                        <input type="number" class="form-control text-center qty-input" value="1" min="1" max="<?php echo $product['stock']; ?>">
                                        <button class="btn btn-outline-secondary increase" type="button">+</button>
                                    </div>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="d-grid gap-2">
                                    <a href="product-detail.php?id=<?php echo $product['product_id']; ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-eye me-2"></i>View Detail
                                    </a>
                                    <button class="btn btn-success add-to-cart-btn" data-product-id="<?php echo $product['product_id']; ?>">
                                        <i class="fas fa-cart-plus me-2"></i>Add to Cart
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php 
                        endforeach;
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Database error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>

                <!-- Test Controls -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Quantity Tests</h6>
                                <button class="btn btn-info btn-sm me-2" onclick="testQuantityControls()">Test All Quantity Controls</button>
                                <button class="btn btn-warning btn-sm" onclick="resetAllQuantities()">Reset All Quantities</button>
                            </div>
                            <div class="col-md-6">
                                <h6>Cart Tests</h6>
                                <button class="btn btn-success btn-sm me-2" onclick="testAllAddToCart()">Test All Add to Cart</button>
                                <button class="btn btn-secondary btn-sm" onclick="checkCartCount()">Check Cart Count</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Results -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-list me-2"></i>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results">
                            <p class="text-muted">Run tests to see results here...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Universal Handler -->
    <script src="assets/js/universal-product-handler.js"></script>
    
    <!-- Test Scripts -->
    <script>
        // Check system status
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
        });

        function checkSystemStatus() {
            const statusDiv = document.getElementById('status-check');
            let status = [];
            
            // Check if universal handler is loaded
            if (window.universalProductHandler) {
                status.push('✅ Universal Product Handler: Loaded');
            } else {
                status.push('❌ Universal Product Handler: Not Found');
            }
            
            // Check for quantity controls
            const quantityControls = document.querySelectorAll('.quantity-selector');
            status.push(`📊 Quantity Controls Found: ${quantityControls.length}`);
            
            // Check for add to cart buttons
            const addToCartBtns = document.querySelectorAll('.add-to-cart-btn');
            status.push(`🛒 Add to Cart Buttons Found: ${addToCartBtns.length}`);
            
            statusDiv.innerHTML = status.map(s => `<p>${s}</p>`).join('');
        }

        function testQuantityControls() {
            const results = [];
            const quantityInputs = document.querySelectorAll('.qty-input');
            
            quantityInputs.forEach((input, index) => {
                const container = input.closest('.quantity-selector');
                const decreaseBtn = container.querySelector('.decrease');
                const increaseBtn = container.querySelector('.increase');
                
                // Test increase
                const originalValue = parseInt(input.value);
                increaseBtn.click();
                const newValue = parseInt(input.value);
                
                if (newValue > originalValue) {
                    results.push(`✅ Product ${index + 1}: Increase button working`);
                } else {
                    results.push(`❌ Product ${index + 1}: Increase button failed`);
                }
                
                // Test decrease
                if (newValue > 1) {
                    decreaseBtn.click();
                    const finalValue = parseInt(input.value);
                    if (finalValue < newValue) {
                        results.push(`✅ Product ${index + 1}: Decrease button working`);
                    } else {
                        results.push(`❌ Product ${index + 1}: Decrease button failed`);
                    }
                }
            });
            
            displayTestResults('Quantity Controls Test', results);
        }

        function resetAllQuantities() {
            document.querySelectorAll('.qty-input').forEach(input => {
                input.value = 1;
            });
            displayTestResults('Reset Quantities', ['✅ All quantities reset to 1']);
        }

        function testAllAddToCart() {
            const results = [];
            const addToCartBtns = document.querySelectorAll('.add-to-cart-btn');
            
            if (addToCartBtns.length === 0) {
                results.push('❌ No add to cart buttons found');
            } else {
                results.push(`📊 Found ${addToCartBtns.length} add to cart buttons`);
                results.push('🔄 Click any "Add to Cart" button to test functionality');
            }
            
            displayTestResults('Add to Cart Test', results);
        }

        function checkCartCount() {
            const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count');
            const results = [];
            
            if (cartElements.length === 0) {
                results.push('❌ No cart count elements found');
            } else {
                cartElements.forEach((element, index) => {
                    results.push(`📊 Cart Count Element ${index + 1}: ${element.textContent || '0'}`);
                });
            }
            
            displayTestResults('Cart Count Check', results);
        }

        function displayTestResults(testName, results) {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultHtml = `
                <div class="border-bottom pb-2 mb-2">
                    <h6>${testName} - ${timestamp}</h6>
                    ${results.map(result => `<p class="mb-1">${result}</p>`).join('')}
                </div>
            `;
            
            resultsDiv.innerHTML = resultHtml + resultsDiv.innerHTML;
        }
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
