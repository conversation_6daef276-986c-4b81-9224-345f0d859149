<?php
session_start();
require_once 'includes/db_connect.php';
require_once 'includes/functions.php';
// require_once 'includes/StockReservation.php'; // Disabled for now

// Check if user is logged in (check both regular and Firebase login)
if (!isset($_SESSION['user_id']) && !isset($_SESSION['firebase_user_id'])) {
    $_SESSION['redirect_after_login'] = 'checkout.php';
    $_SESSION['alert_type'] = 'warning';
    $_SESSION['alert_message'] = 'Please login to proceed with checkout';
    header('Location: login.php');
    exit;
}

// Get user ID - prioritize local_user_id for Firebase users
$user_id = $_SESSION['local_user_id'] ?? $_SESSION['user_id'];

// For Firebase users, sync if needed
if (isset($_SESSION['firebase_user_id']) && isset($_SESSION['user_email'])) {
    $local_user_id = syncFirebaseUserToDatabase(
        $_SESSION['firebase_user_id'],
        $_SESSION['user_email'],
        $_SESSION['user_name'] ?? null
    );

    if ($local_user_id) {
        $user_id = $local_user_id;
        $_SESSION['local_user_id'] = $local_user_id;
        $_SESSION['user_id'] = $local_user_id; // Update for compatibility
    }
}

// Verify user exists in database
try {
    $stmt = $conn->prepare("SELECT user_id FROM users WHERE user_id = ?");
    $stmt->execute([$user_id]);
    if (!$stmt->fetch()) {
        // User doesn't exist, clear session and redirect
        session_destroy();
        session_start();
        $_SESSION['alert_type'] = 'error';
        $_SESSION['alert_message'] = 'Invalid session. Please log in again.';
        header('Location: login.php');
        exit;
    }
} catch (PDOException $e) {
    error_log("User verification error in checkout: " . $e->getMessage());
    $_SESSION['alert_type'] = 'error';
    $_SESSION['alert_message'] = 'Database error. Please try again.';
    header('Location: login.php');
    exit;
}

// Define default variables
$error_message = isset($_SESSION['checkout_error']) ? $_SESSION['checkout_error'] : null;
$success_message = isset($_SESSION['checkout_success']) ? $_SESSION['checkout_success'] : null;

// Clear session messages
unset($_SESSION['checkout_error']);
unset($_SESSION['checkout_success']);

// Get user's cart items from database
$cart_items = [];
$subtotal = 0;
$cart_id = null;

try {
    global $conn;

    // Get user's cart using helper function
    $cart_id = getOrCreateCart($user_id);

    if (!$cart_id) {
        // Failed to get cart
        $_SESSION['alert_type'] = 'error';
        $_SESSION['alert_message'] = 'Unable to access your cart. Please try again.';
        header('Location: cart.php');
        exit;
    }

    // Get cart items with product details
    // Handle both 'name' and 'NAME' column variations
    $stmt = $conn->prepare("
        SELECT ci.cart_item_id, ci.product_id, ci.quantity,
               COALESCE(p.name, p.NAME) as name, p.price, p.image, p.stock
        FROM cart_items ci
        JOIN products p ON ci.product_id = p.product_id
        WHERE ci.cart_id = ?
    ");
    $stmt->execute([$cart_id]);
    $cart_items = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($cart_items) === 0) {
        // Cart exists but has no items
        $_SESSION['alert_type'] = 'info';
        $_SESSION['alert_message'] = 'Your cart is empty. Please add items before checkout.';
        header('Location: cart.php');
        exit;
    }

    // Calculate subtotal
    foreach ($cart_items as $item) {
        $subtotal += $item['price'] * $item['quantity'];
    }

} catch (PDOException $e) {
    error_log("Checkout cart error: " . $e->getMessage());
    $_SESSION['alert_type'] = 'danger';
    $_SESSION['alert_message'] = 'An error occurred while retrieving your cart. Please try again.';
    header('Location: cart.php');
    exit;
}

// Shipping methods
$shipping_methods = [
    'standard' => [
        'name' => 'Standard Shipping (2-3 days)',
        'cost' => 10000
    ],
    'express' => [
        'name' => 'Express Shipping (1 day)',
        'cost' => 25000
    ]
];

// Payment methods - More realistic Indonesian payment options
$payment_methods = [
    'bank_transfer' => [
        'name' => 'Transfer Bank',
        'description' => 'Transfer ke rekening bank kami. Konfirmasi pembayaran dalam 24 jam.',
        'icon' => 'fas fa-university',
        'fee' => 0,
        'banks' => [
            'bca' => ['name' => 'BCA', 'account' => '**********', 'holder' => 'PT Tewuneed Indonesia'],
            'mandiri' => ['name' => 'Mandiri', 'account' => '*************', 'holder' => 'PT Tewuneed Indonesia'],
            'bni' => ['name' => 'BNI', 'account' => '**********', 'holder' => 'PT Tewuneed Indonesia'],
            'bri' => ['name' => 'BRI', 'account' => '***************', 'holder' => 'PT Tewuneed Indonesia']
        ]
    ],
    'virtual_account' => [
        'name' => 'Virtual Account',
        'description' => 'Bayar melalui ATM, mobile banking, atau internet banking.',
        'icon' => 'fas fa-credit-card',
        'fee' => 4000,
        'banks' => ['BCA', 'Mandiri', 'BNI', 'BRI', 'Permata']
    ],
    'ewallet' => [
        'name' => 'E-Wallet',
        'description' => 'Bayar dengan dompet digital favorit Anda.',
        'icon' => 'fas fa-mobile-alt',
        'fee' => 0,
        'providers' => [
            'gopay' => ['name' => 'GoPay', 'icon' => 'fab fa-google-pay'],
            'ovo' => ['name' => 'OVO', 'icon' => 'fas fa-wallet'],
            'dana' => ['name' => 'DANA', 'icon' => 'fas fa-coins'],
            'linkaja' => ['name' => 'LinkAja', 'icon' => 'fas fa-link'],
            'shopeepay' => ['name' => 'ShopeePay', 'icon' => 'fas fa-shopping-bag']
        ]
    ],
    'credit_card' => [
        'name' => 'Kartu Kredit/Debit',
        'description' => 'Visa, Mastercard, JCB. Pembayaran aman dengan enkripsi SSL.',
        'icon' => 'fas fa-credit-card',
        'fee' => 2.9, // percentage
        'cards' => ['Visa', 'Mastercard', 'JCB', 'American Express']
    ],
    'qris' => [
        'name' => 'QRIS',
        'description' => 'Scan QR code dengan aplikasi bank atau e-wallet apapun.',
        'icon' => 'fas fa-qrcode',
        'fee' => 0.7 // percentage
    ],
    'installment' => [
        'name' => 'Cicilan 0%',
        'description' => 'Cicilan 0% untuk kartu kredit tertentu (min. pembelian Rp 500.000).',
        'icon' => 'fas fa-calendar-alt',
        'fee' => 0,
        'min_amount' => 500000,
        'terms' => [3, 6, 12, 24]
    ],
    'cod' => [
        'name' => 'Bayar di Tempat (COD)',
        'description' => 'Bayar tunai saat barang diterima. Tersedia untuk area tertentu.',
        'icon' => 'fas fa-hand-holding-usd',
        'fee' => 5000,
        'areas' => ['Jakarta', 'Bogor', 'Depok', 'Tangerang', 'Bekasi']
    ]
];

// Process checkout form submission BEFORE including header
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("=== CHECKOUT POST REQUEST RECEIVED ===");
    error_log("POST data: " . print_r($_POST, true));
    error_log("Session user_id: " . ($user_id ?? 'NOT SET'));
    error_log("Cart items count: " . count($cart_items));

    // Force display error for debugging
    ini_set('display_errors', 1);
    error_reporting(E_ALL);

    try {
        // Validate input
        error_log("=== VALIDATING INPUT ===");
        $required_fields = ['shipping_name', 'shipping_email', 'shipping_phone', 'shipping_address'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                error_log("Missing required field: " . $field);
                throw new Exception("Please fill in all required fields: " . $field);
            }
            error_log("Field {$field}: " . $_POST[$field]);
        }

        // Validate email
        error_log("=== VALIDATING EMAIL ===");
        if (!filter_var($_POST['shipping_email'], FILTER_VALIDATE_EMAIL)) {
            error_log("Invalid email: " . $_POST['shipping_email']);
            throw new Exception("Invalid email address");
        }
        error_log("Email valid: " . $_POST['shipping_email']);

        // Validate phone
        error_log("=== VALIDATING PHONE ===");
        if (!preg_match('/^[0-9]{10,15}$/', $_POST['shipping_phone'])) {
            error_log("Invalid phone: " . $_POST['shipping_phone']);
            throw new Exception("Invalid phone number");
        }
        error_log("Phone valid: " . $_POST['shipping_phone']);

        // Get shipping cost
        $shipping_method = $_POST['shipping_method'] ?? 'standard';
        $shipping_cost = $shipping_methods[$shipping_method]['cost'] ?? 10000;

        // Get payment method
        $payment_method = $_POST['payment_method'] ?? 'bank_transfer';

        // Calculate total
        $total = $subtotal + $shipping_cost;

        // Start transaction
        error_log("=== STARTING DATABASE TRANSACTION ===");
        error_log("Total amount: " . $total);
        error_log("Payment method: " . $payment_method);
        error_log("User ID: " . $user_id);

        $conn->beginTransaction();

        try {
            // Create order with or without order_number based on database structure
            try {
                // Try inserting with order_number first
                $order_number = generateOrderNumber(); // Generate unique order number
                $stmt = $conn->prepare("
                    INSERT INTO orders (
                        order_number, user_id, shipping_name, shipping_email, shipping_phone, shipping_address,
                        payment_method, order_status, order_date, shipping_method, shipping_cost, subtotal, total_amount
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, 'pending', NOW(), ?, ?, ?, ?
                    )
                ");

                $stmt->execute([
                    $order_number,
                    $user_id,
                    $_POST['shipping_name'],
                    $_POST['shipping_email'],
                    $_POST['shipping_phone'],
                    $_POST['shipping_address'],
                    $_POST['payment_method'],
                    $shipping_method,
                    $shipping_cost,
                    $subtotal,
                    $total
                ]);
            } catch (PDOException $orderEx) {
                // If failed because of order_number, try without it
                if (strpos($orderEx->getMessage(), 'Duplicate entry') !== false || strpos($orderEx->getMessage(), 'order_number') !== false) {
                    $stmt = $conn->prepare("
                        INSERT INTO orders (
                            user_id, shipping_name, shipping_email, shipping_phone, shipping_address,
                            payment_method, order_status, order_date, shipping_method, shipping_cost, subtotal, total_amount
                        ) VALUES (
                            ?, ?, ?, ?, ?, ?, 'pending', NOW(), ?, ?, ?, ?
                        )
                    ");

                    $stmt->execute([
                        $user_id,
                        $_POST['shipping_name'],
                        $_POST['shipping_email'],
                        $_POST['shipping_phone'],
                        $_POST['shipping_address'],
                        $_POST['payment_method'],
                        $shipping_method,
                        $shipping_cost,
                        $subtotal,
                        $total
                    ]);
                } else {
                    // If it's another error, rethrow it
                    throw $orderEx;
                }
            }

            $order_id = $conn->lastInsertId();

            // Create order notification
            require_once 'includes/order_status_functions.php';
            createOrderNotification($conn, $order_id, $user_id, 'pending');

            // Create payment notification
            createPaymentNotification($user_id, $order_id, $_POST['payment_method'], $total, 'pending');

            // Add order items - use unit_price and total_price based on database schema
            $stmt = $conn->prepare("
                INSERT INTO order_items (
                    order_id, product_id, quantity, unit_price, total_price
                ) VALUES (
                    ?, ?, ?, ?, ?
                )
            ");

            // Add order items and reduce stock
            foreach ($cart_items as $item) {
                // Check stock availability before adding to order
                $stmt_stock = $conn->prepare("SELECT stock FROM products WHERE product_id = ?");
                $stmt_stock->execute([$item['product_id']]);
                $current_stock = $stmt_stock->fetchColumn();

                if ($current_stock < $item['quantity']) {
                    throw new Exception("Insufficient stock for product: " . $item['name'] . ". Available: " . $current_stock . ", Requested: " . $item['quantity']);
                }

                // Add order item with unit_price and total_price
                $unit_price = $item['price'];
                $total_price = $item['price'] * $item['quantity'];

                $stmt->execute([
                    $order_id,
                    $item['product_id'],
                    $item['quantity'],
                    $unit_price,
                    $total_price
                ]);

                // Reduce actual stock
                $stmt_update_stock = $conn->prepare("UPDATE products SET stock = stock - ? WHERE product_id = ?");
                $stmt_update_stock->execute([$item['quantity'], $item['product_id']]);

                error_log("Reduced stock for product {$item['product_id']} by {$item['quantity']}");
            }

            // Clear the user's cart after successful order
            $stmt = $conn->prepare("DELETE FROM cart_items WHERE cart_id = ?");
            $stmt->execute([$cart_id]);

            // Update order status - handle different column names
            try {
                // Try with 'order_status' column first
                $stmt = $conn->prepare("UPDATE orders SET order_status = 'dibuat' WHERE order_id = ?");
                $stmt->execute([$order_id]);
            } catch (PDOException $e) {
                // If that fails, try with 'STATUS' column
                try {
                    $stmt = $conn->prepare("UPDATE orders SET STATUS = 'pending' WHERE order_id = ?");
                    $stmt->execute([$order_id]);
                } catch (PDOException $e2) {
                    // If that also fails, try with 'status' column
                    $stmt = $conn->prepare("UPDATE orders SET status = 'dibuat' WHERE order_id = ?");
                    $stmt->execute([$order_id]);
                }
            }

            // Commit transaction
            $conn->commit();

            // Store order info in session for confirmation page
            $_SESSION['order_completed'] = [
                'order_id' => $order_id,
                'order_number' => $order_number ?? null,
                'total' => $subtotal + $shipping_cost,
                'payment_method' => $_POST['payment_method']
            ];

            // Redirect based on payment method - BEFORE header.php is included
            $_SESSION['last_order_id'] = $order_id;

            error_log("Order created successfully. Order ID: {$order_id}, Payment Method: {$payment_method}");

            // Clear any output buffer to prevent redirect issues
            if (ob_get_level()) {
                ob_end_clean();
            }

            switch ($payment_method) {
                case 'qris':
                    $redirect_url = 'payment-qris.php?order_id=' . $order_id;
                    break;
                case 'virtual_account':
                    $redirect_url = 'payment-va.php?order_id=' . $order_id;
                    break;
                case 'bank_transfer':
                case 'credit_card':
                case 'ewallet':
                case 'installment':
                case 'cod':
                default:
                    $redirect_url = 'order_details.php?id=' . $order_id;
                    break;
            }

            error_log("Redirecting to: {$redirect_url}");

            // Use the redirect function for better handling
            if (!headers_sent()) {
                header('Location: ' . $redirect_url);
                exit;
            } else {
                // Fallback JavaScript redirect
                echo '<script>window.location.href="' . htmlspecialchars($redirect_url) . '";</script>';
                exit;
            }

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            error_log("Transaction rollback due to error: " . $e->getMessage());
            throw $e;
        }
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        error_log("Checkout error: " . $error_message);
        error_log("POST data when error occurred: " . print_r($_POST, true));

        // Store error in session instead of displaying it directly
        $_SESSION['checkout_error'] = $error_message;
        // No redirect here, just continue to display the form with error
    }
}

// Now that all redirects are handled, include the header
$page = 'checkout';
$page_title = 'Checkout';
require_once 'includes/header.php';

?>

<div class="container py-4">
    <h1 class="mb-4">Checkout</h1>

    <?php if ($error_message): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <?php if ($success_message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>

    <div class="row">
        <!-- Checkout Form -->
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Shipping Information</h4>
                </div>
                <div class="card-body">
                    <form method="post" action="checkout.php" id="checkout-form" onsubmit="return validateCheckoutForm();">
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label for="shipping_name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="shipping_name" name="shipping_name" required
                                       value="<?php echo isset($_POST['shipping_name']) ? htmlspecialchars($_POST['shipping_name']) :
                                       (isset($_SESSION['user_fullname']) ? htmlspecialchars($_SESSION['user_fullname']) : ''); ?>">
                            </div>
                            <div class="col-md-6">
                                <label for="shipping_email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="shipping_email" name="shipping_email" required
                                       value="<?php echo isset($_POST['shipping_email']) ? htmlspecialchars($_POST['shipping_email']) :
                                       (isset($_SESSION['user_email']) ? htmlspecialchars($_SESSION['user_email']) : ''); ?>">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="shipping_phone" class="form-label">Phone Number *</label>
                            <input type="tel" class="form-control" id="shipping_phone" name="shipping_phone" required
                                   placeholder="e.g., 08**********"
                                   value="<?php echo isset($_POST['shipping_phone']) ? htmlspecialchars($_POST['shipping_phone']) : ''; ?>">
                            <div class="form-text">Enter a valid phone number (10-15 digits)</div>
                        </div>

                        <div class="mb-4">
                            <label for="shipping_address" class="form-label">Shipping Address *</label>
                            <textarea class="form-control" id="shipping_address" name="shipping_address" rows="3" required><?php echo isset($_POST['shipping_address']) ? htmlspecialchars($_POST['shipping_address']) : ''; ?></textarea>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Shipping Method *</label>
                            <?php foreach ($shipping_methods as $key => $method): ?>
                            <div class="form-check mb-2">
                                <input type="radio"
                                       class="form-check-input shipping-method"
                                       name="shipping_method"
                                       id="shipping_<?php echo $key; ?>"
                                       value="<?php echo $key; ?>"
                                       data-cost="<?php echo $method['cost']; ?>"
                                       <?php echo (!isset($_POST['shipping_method']) && $key === 'standard') || (isset($_POST['shipping_method']) && $_POST['shipping_method'] === $key) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="shipping_<?php echo $key; ?>">
                                    <?php echo $method['name']; ?> - Rp <?php echo number_format($method['cost'], 0, ',', '.'); ?>
                                </label>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Payment Method *</label>
                            <div class="payment-methods">
                                <?php foreach ($payment_methods as $key => $method): ?>
                                <div class="payment-method-card mb-3" data-method="<?php echo $key; ?>">
                                    <div class="form-check">
                                        <input type="radio"
                                               class="form-check-input payment-method-radio"
                                               name="payment_method"
                                               id="payment_<?php echo $key; ?>"
                                               value="<?php echo $key; ?>"
                                               data-fee="<?php echo $method['fee']; ?>"
                                               data-fee-type="<?php echo is_numeric($method['fee']) && $method['fee'] < 10 ? 'percentage' : 'fixed'; ?>"
                                               <?php echo (!isset($_POST['payment_method']) && $key === 'bank_transfer') || (isset($_POST['payment_method']) && $_POST['payment_method'] === $key) ? 'checked' : ''; ?>>
                                        <label class="form-check-label w-100" for="payment_<?php echo $key; ?>">
                                            <div class="d-flex align-items-center">
                                                <div class="payment-icon me-3">
                                                    <i class="<?php echo $method['icon']; ?> fa-2x text-primary"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="mb-1"><?php echo $method['name']; ?></h6>
                                                    <small class="text-muted"><?php echo $method['description']; ?></small>
                                                    <?php if ($method['fee'] > 0): ?>
                                                    <div class="mt-1">
                                                        <span class="badge bg-warning text-dark">
                                                            <?php if (is_numeric($method['fee']) && $method['fee'] < 10): ?>
                                                                Fee: <?php echo $method['fee']; ?>%
                                                            <?php else: ?>
                                                                Fee: Rp <?php echo number_format($method['fee'], 0, ',', '.'); ?>
                                                            <?php endif; ?>
                                                        </span>
                                                    </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </label>
                                    </div>

                                    <!-- Payment Method Details -->
                                    <div class="payment-details mt-3" id="details_<?php echo $key; ?>" style="display: none;">
                                        <?php if ($key === 'bank_transfer'): ?>
                                            <div class="bank-accounts">
                                                <h6>Pilih Bank:</h6>
                                                <?php foreach ($method['banks'] as $bank_key => $bank): ?>
                                                <div class="bank-option mb-2 p-2 border rounded">
                                                    <div class="form-check">
                                                        <input type="radio" class="form-check-input" name="selected_bank" value="<?php echo $bank_key; ?>" id="bank_<?php echo $bank_key; ?>">
                                                        <label class="form-check-label" for="bank_<?php echo $bank_key; ?>">
                                                            <strong><?php echo $bank['name']; ?></strong><br>
                                                            <small>No. Rekening: <?php echo $bank['account']; ?></small><br>
                                                            <small>A.n: <?php echo $bank['holder']; ?></small>
                                                        </label>
                                                    </div>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php elseif ($key === 'virtual_account'): ?>
                                            <div class="va-banks">
                                                <h6>Bank yang Tersedia:</h6>
                                                <div class="d-flex flex-wrap gap-2">
                                                    <?php foreach ($method['banks'] as $bank): ?>
                                                    <span class="badge bg-secondary"><?php echo $bank; ?></span>
                                                    <?php endforeach; ?>
                                                </div>
                                                <small class="text-muted mt-2 d-block">Virtual Account akan digenerate setelah order dikonfirmasi</small>
                                            </div>
                                        <?php elseif ($key === 'ewallet'): ?>
                                            <div class="ewallet-providers">
                                                <h6>Pilih E-Wallet:</h6>
                                                <div class="row">
                                                    <?php foreach ($method['providers'] as $provider_key => $provider): ?>
                                                    <div class="col-6 col-md-4 mb-2">
                                                        <div class="form-check">
                                                            <input type="radio" class="form-check-input" name="selected_ewallet" value="<?php echo $provider_key; ?>" id="ewallet_<?php echo $provider_key; ?>">
                                                            <label class="form-check-label text-center d-block" for="ewallet_<?php echo $provider_key; ?>">
                                                                <i class="<?php echo $provider['icon']; ?> fa-2x d-block mb-1"></i>
                                                                <small><?php echo $provider['name']; ?></small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php elseif ($key === 'credit_card'): ?>
                                            <div class="credit-card-form">
                                                <h6>Informasi Kartu:</h6>
                                                <div class="row">
                                                    <div class="col-md-8 mb-3">
                                                        <label class="form-label">Nomor Kartu</label>
                                                        <input type="text" class="form-control" name="card_number" placeholder="1234 5678 9012 3456" maxlength="19">
                                                    </div>
                                                    <div class="col-md-4 mb-3">
                                                        <label class="form-label">CVV</label>
                                                        <input type="text" class="form-control" name="card_cvv" placeholder="123" maxlength="4">
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">Bulan/Tahun</label>
                                                        <input type="text" class="form-control" name="card_expiry" placeholder="MM/YY" maxlength="5">
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">Nama di Kartu</label>
                                                        <input type="text" class="form-control" name="card_holder" placeholder="JOHN DOE">
                                                    </div>
                                                </div>
                                                <div class="supported-cards">
                                                    <small class="text-muted">Kartu yang didukung:</small>
                                                    <div class="d-flex gap-2 mt-1">
                                                        <?php foreach ($method['cards'] as $card): ?>
                                                        <span class="badge bg-light text-dark"><?php echo $card; ?></span>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php elseif ($key === 'installment'): ?>
                                            <div class="installment-options">
                                                <h6>Pilih Tenor Cicilan:</h6>
                                                <?php if ($subtotal >= $method['min_amount']): ?>
                                                    <?php foreach ($method['terms'] as $term): ?>
                                                    <div class="form-check">
                                                        <input type="radio" class="form-check-input" name="installment_term" value="<?php echo $term; ?>" id="term_<?php echo $term; ?>">
                                                        <label class="form-check-label" for="term_<?php echo $term; ?>">
                                                            <?php echo $term; ?> bulan - Rp <?php echo number_format($subtotal / $term, 0, ',', '.'); ?>/bulan
                                                        </label>
                                                    </div>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <div class="alert alert-warning">
                                                        Minimum pembelian untuk cicilan adalah Rp <?php echo number_format($method['min_amount'], 0, ',', '.'); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        <?php elseif ($key === 'cod'): ?>
                                            <div class="cod-info">
                                                <h6>Area yang Tersedia:</h6>
                                                <div class="d-flex flex-wrap gap-2">
                                                    <?php foreach ($method['areas'] as $area): ?>
                                                    <span class="badge bg-success"><?php echo $area; ?></span>
                                                    <?php endforeach; ?>
                                                </div>
                                                <div class="alert alert-info mt-2">
                                                    <small><i class="fas fa-info-circle me-1"></i> Pastikan alamat pengiriman Anda berada di area yang tersedia</small>
                                                </div>
                                            </div>
                                        <?php elseif ($key === 'qris'): ?>
                                            <div class="qris-info">
                                                <div class="alert alert-info">
                                                    <i class="fas fa-qrcode me-2"></i>
                                                    QR Code akan ditampilkan setelah order dikonfirmasi. Scan dengan aplikasi bank atau e-wallet apapun.
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="cart.php" class="btn btn-outline-secondary">Back to Cart</a>

                            <!-- Main submit button -->
                            <button type="submit" class="btn btn-primary btn-lg" id="place-order-btn">
                                <span class="btn-text">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    Place Order
                                </span>
                                <span class="btn-loading d-none">
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    Processing Order...
                                </span>
                            </button>

                            <!-- Emergency backup submit button -->
                            <button type="button" class="btn btn-outline-warning ms-2" onclick="forceSubmitCheckout();" title="Force Submit if main button fails">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Force Submit
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="col-md-4">
            <div class="card sticky-top" style="top: 20px; z-index: 1;">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Order Summary</h5>
                </div>
                <div class="card-body">
                    <div id="checkout-summary">
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <?php foreach ($cart_items as $item): ?>
                                    <tr>
                                        <td class="w-50">
                                            <small class="d-block fw-bold"><?php echo htmlspecialchars($item['name']); ?></small>
                                            <small class="text-muted">Qty: <?php echo $item['quantity']; ?> x Rp <?php echo number_format($item['price'], 0, ',', '.'); ?></small>
                                        </td>
                                        <td class="text-end">
                                            <small>Rp <?php echo number_format($item['price'] * $item['quantity'], 0, ',', '.'); ?></small>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>

                                    <tr class="border-top">
                                        <td>Subtotal</td>
                                        <td class="text-end" id="summary-subtotal">Rp <?php echo number_format($subtotal, 0, ',', '.'); ?></td>
                                    </tr>
                                    <tr>
                                        <td>Shipping</td>
                                        <td class="text-end" id="summary-shipping">Rp <?php echo number_format($shipping_methods['standard']['cost'], 0, ',', '.'); ?></td>
                                    </tr>
                                    <tr class="fw-bold">
                                        <td>Total</td>
                                        <td class="text-end" id="summary-total">Rp <?php echo number_format($subtotal + $shipping_methods['standard']['cost'], 0, ',', '.'); ?></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.payment-method-card {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.payment-method-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.1);
}

.payment-method-card.selected {
    border-color: #007bff;
    background-color: #f8f9ff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
}

.payment-icon {
    min-width: 60px;
    text-align: center;
}

.payment-details {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #007bff;
}

.bank-option {
    transition: all 0.2s ease;
}

.bank-option:hover {
    background-color: #f8f9fa;
    border-color: #007bff !important;
}

.bank-option input[type="radio"]:checked + label {
    color: #007bff;
    font-weight: 500;
}

.credit-card-form input {
    border-radius: 8px;
}

.ewallet-providers .form-check-label {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px 10px;
    transition: all 0.2s ease;
}

.ewallet-providers .form-check-label:hover {
    border-color: #007bff;
    background-color: #f8f9ff;
}

.ewallet-providers input[type="radio"]:checked + label {
    border-color: #007bff;
    background-color: #f8f9ff;
    color: #007bff;
}

.payment-fee-display {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    margin-top: 10px;
}

.order-summary-sticky {
    position: sticky;
    top: 20px;
    z-index: 10;
}

@media (max-width: 768px) {
    .payment-method-card {
        padding: 12px;
    }

    .payment-icon {
        min-width: 50px;
    }

    .payment-icon i {
        font-size: 1.5rem !important;
    }
}
</style>

<script>
// Simple form validation function
function validateCheckoutForm() {
    console.log('=== VALIDATING CHECKOUT FORM ===');

    // Check payment method
    const selectedPayment = document.querySelector('.payment-method-radio:checked');
    if (!selectedPayment) {
        alert('Please select a payment method');
        return false;
    }

    // Check required fields
    const requiredFields = [
        { name: 'shipping_name', label: 'Full Name' },
        { name: 'shipping_email', label: 'Email' },
        { name: 'shipping_phone', label: 'Phone' },
        { name: 'shipping_address', label: 'Address' }
    ];

    for (let field of requiredFields) {
        const input = document.querySelector(`[name="${field.name}"]`);
        if (!input || !input.value.trim()) {
            alert(`Please fill in ${field.label}`);
            if (input) input.focus();
            return false;
        }
    }

    console.log('Form validation passed, allowing submission...');
    return true;
}

// Force submit function for emergency button
function forceSubmitCheckout() {
    console.log('=== FORCE SUBMIT CHECKOUT ===');

    const form = document.getElementById('checkout-form');
    if (!form) {
        alert('Form not found!');
        return;
    }

    // Auto-select first payment method if none selected
    const selectedPayment = document.querySelector('.payment-method-radio:checked');
    if (!selectedPayment) {
        const firstPayment = document.querySelector('.payment-method-radio');
        if (firstPayment) {
            firstPayment.checked = true;
            console.log('Auto-selected payment method:', firstPayment.value);
        } else {
            alert('No payment methods available!');
            return;
        }
    }

    // Fill in required fields with default values if empty
    const requiredFields = [
        { name: 'shipping_name', default: 'Test User' },
        { name: 'shipping_email', default: '<EMAIL>' },
        { name: 'shipping_phone', default: '08**********' },
        { name: 'shipping_address', default: 'Test Address' }
    ];

    requiredFields.forEach(field => {
        const input = document.querySelector(`[name="${field.name}"]`);
        if (input && !input.value.trim()) {
            input.value = field.default;
            console.log(`Auto-filled ${field.name} with: ${field.default}`);
        }
    });

    console.log('Force submitting form...');
    form.submit();
}

// Debug function
function debugCheckout() {
    console.log('=== CHECKOUT DEBUG ===');

    const form = document.getElementById('checkout-form');
    const submitBtn = document.getElementById('place-order-btn');
    const selectedPayment = document.querySelector('.payment-method-radio:checked');

    console.log('Form element:', form);
    console.log('Submit button:', submitBtn);
    console.log('Selected payment:', selectedPayment);
    console.log('Form action:', form ? form.action : 'No form');
    console.log('Form method:', form ? form.method : 'No form');

    // Check required fields
    const requiredFields = ['shipping_name', 'shipping_email', 'shipping_phone', 'shipping_address'];
    const missingFields = [];

    requiredFields.forEach(field => {
        const input = document.querySelector(`[name="${field}"]`);
        if (!input || !input.value.trim()) {
            missingFields.push(field);
        }
    });

    if (missingFields.length > 0) {
        alert('Missing required fields: ' + missingFields.join(', '));
        return;
    }

    if (!selectedPayment) {
        alert('No payment method selected');
        return;
    }

    alert('All validations passed! Form should be ready to submit.');
}



document.addEventListener('DOMContentLoaded', function() {
    // Format number as currency
    function formatCurrency(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }

    // Get elements
    const shippingMethods = document.querySelectorAll('.shipping-method');
    const paymentMethods = document.querySelectorAll('.payment-method-radio');
    const summaryShipping = document.getElementById('summary-shipping');
    const summaryTotal = document.getElementById('summary-total');
    const subtotal = <?php echo $subtotal; ?>;
    let currentShippingCost = <?php echo $shipping_methods['standard']['cost']; ?>;
    let currentPaymentFee = 0;

    // Payment method handling
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Remove selected class from all cards
            document.querySelectorAll('.payment-method-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selected class to current card
            this.closest('.payment-method-card').classList.add('selected');

            // Hide all payment details
            document.querySelectorAll('.payment-details').forEach(detail => {
                detail.style.display = 'none';
            });

            // Show current payment details
            const detailsId = 'details_' + this.value;
            const detailsElement = document.getElementById(detailsId);
            if (detailsElement) {
                detailsElement.style.display = 'block';
            }

            // Calculate payment fee
            const fee = parseFloat(this.dataset.fee) || 0;
            const feeType = this.dataset.feeType;

            if (feeType === 'percentage') {
                currentPaymentFee = (subtotal * fee) / 100;
            } else {
                currentPaymentFee = fee;
            }

            updateOrderSummary();

            // Show/hide payment fee display
            showPaymentFeeDisplay(this.value, fee, feeType);
        });
    });

    // Update summary when shipping method changes
    shippingMethods.forEach(method => {
        method.addEventListener('change', function() {
            currentShippingCost = parseInt(this.dataset.cost);
            updateOrderSummary();
        });
    });

    // Update order summary
    function updateOrderSummary() {
        const total = subtotal + currentShippingCost + currentPaymentFee;

        // Update shipping
        summaryShipping.textContent = 'Rp ' + formatCurrency(currentShippingCost);

        // Update or add payment fee row
        updatePaymentFeeRow();

        // Update total
        summaryTotal.textContent = 'Rp ' + formatCurrency(total);
    }

    function updatePaymentFeeRow() {
        // Remove existing payment fee row
        const existingFeeRow = document.getElementById('payment-fee-row');
        if (existingFeeRow) {
            existingFeeRow.remove();
        }

        // Add payment fee row if there's a fee
        if (currentPaymentFee > 0) {
            const feeRow = document.createElement('tr');
            feeRow.id = 'payment-fee-row';
            feeRow.innerHTML = `
                <td>Payment Fee</td>
                <td class="text-end">Rp ${formatCurrency(currentPaymentFee)}</td>
            `;

            // Insert before total row
            const totalRow = summaryTotal.closest('tr');
            totalRow.parentNode.insertBefore(feeRow, totalRow);
        }
    }

    function showPaymentFeeDisplay(paymentMethod, fee, feeType) {
        // Remove existing fee display
        const existingDisplay = document.querySelector('.payment-fee-display');
        if (existingDisplay) {
            existingDisplay.remove();
        }

        // Show fee display if there's a fee
        if (fee > 0) {
            const feeDisplay = document.createElement('div');
            feeDisplay.className = 'payment-fee-display';

            let feeText = '';
            if (feeType === 'percentage') {
                feeText = `Payment fee: ${fee}% (Rp ${formatCurrency(currentPaymentFee)})`;
            } else {
                feeText = `Payment fee: Rp ${formatCurrency(fee)}`;
            }

            feeDisplay.innerHTML = `
                <i class="fas fa-info-circle me-2"></i>
                ${feeText}
            `;

            // Add after the selected payment method card
            const selectedCard = document.querySelector('.payment-method-card.selected');
            if (selectedCard) {
                selectedCard.appendChild(feeDisplay);
            }
        }
    }

    // Credit card number formatting
    const cardNumberInput = document.querySelector('input[name="card_number"]');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function() {
            let value = this.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            this.value = formattedValue;
        });
    }

    // Card expiry formatting
    const cardExpiryInput = document.querySelector('input[name="card_expiry"]');
    if (cardExpiryInput) {
        cardExpiryInput.addEventListener('input', function() {
            let value = this.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            this.value = value;
        });
    }

    // CVV input restriction
    const cvvInput = document.querySelector('input[name="card_cvv"]');
    if (cvvInput) {
        cvvInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    }



    // Simplified form validation and submission handling
    const checkoutForm = document.getElementById('checkout-form');
    const placeOrderBtn = document.getElementById('place-order-btn');

    console.log('=== INITIALIZING CHECKOUT FORM ===');
    console.log('Checkout form element:', checkoutForm);
    console.log('Place order button:', placeOrderBtn);

    if (checkoutForm && placeOrderBtn) {
        console.log('Form and button found, ready for submission');

        // Add simple submit event listener for loading state
        checkoutForm.addEventListener('submit', function(e) {
            console.log('=== FORM SUBMITTING ===');

            // Show loading state
            if (placeOrderBtn) {
                placeOrderBtn.disabled = true;
                const btnText = placeOrderBtn.querySelector('.btn-text');
                const btnLoading = placeOrderBtn.querySelector('.btn-loading');
                if (btnText) btnText.classList.add('d-none');
                if (btnLoading) btnLoading.classList.remove('d-none');
            }
        });
    } else {
        console.log('=== ERROR: Form or button not found! ===');
        console.log('Available forms:', document.querySelectorAll('form'));
        console.log('Available buttons:', document.querySelectorAll('button'));
    }

    // Initialize first payment method if none selected
    const firstPaymentMethod = document.querySelector('.payment-method-radio:checked');
    if (!firstPaymentMethod) {
        const firstPayment = document.querySelector('.payment-method-radio');
        if (firstPayment) {
            firstPayment.checked = true;
            firstPayment.dispatchEvent(new Event('change'));
        }
    } else {
        firstPaymentMethod.dispatchEvent(new Event('change'));
    }
});
</script>

<?php require_once 'includes/footer.php'; ?>