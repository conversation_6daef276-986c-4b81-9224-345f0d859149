<?php
/**
 * Check Database Structure
 * This script checks what tables exist and their structure
 */

require_once 'config.php';
require_once 'includes/db_connect.php';

echo "<h1>🔍 Database Structure Check</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: #28a745; }
.error { color: #dc3545; }
.info { color: #17a2b8; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>";

try {
    // Check what tables exist
    echo "<h2>📋 Available Tables</h2>";
    $stmt = $conn->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Check categories table
    if (in_array('categories', $tables)) {
        echo "<h3>🏷️ Categories Table</h3>";
        $stmt = $conn->query("SELECT * FROM categories LIMIT 5");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($categories)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Slug</th><th>Description</th></tr>";
            foreach ($categories as $cat) {
                echo "<tr>";
                echo "<td>{$cat['category_id']}</td>";
                echo "<td>" . (isset($cat['name']) ? $cat['name'] : 'N/A') . "</td>";
                echo "<td>" . (isset($cat['slug']) ? $cat['slug'] : 'N/A') . "</td>";
                echo "<td>" . substr((isset($cat['description']) ? $cat['description'] : ''), 0, 50) . "...</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        $stmt = $conn->query("SELECT COUNT(*) as total FROM categories");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p class='info'>Total categories: {$result['total']}</p>";
    }
    
    // Check products table
    if (in_array('products', $tables)) {
        echo "<h3>📦 Products Table</h3>";
        $stmt = $conn->query("SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.category_id WHERE p.is_active = 1 LIMIT 5");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($products)) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>Price</th><th>Stock</th></tr>";
            foreach ($products as $product) {
                echo "<tr>";
                echo "<td>{$product['product_id']}</td>";
                echo "<td>" . substr((isset($product['name']) ? $product['name'] : 'N/A'), 0, 30) . "...</td>";
                echo "<td>" . (isset($product['category_name']) ? $product['category_name'] : 'No Category') . "</td>";
                echo "<td>Rp " . number_format((isset($product['price']) ? $product['price'] : 0), 0, ',', '.') . "</td>";
                echo "<td>" . (isset($product['stock']) ? $product['stock'] : 0) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        $stmt = $conn->query("SELECT COUNT(*) as total FROM products WHERE is_active = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p class='info'>Total active products: {$result['total']}</p>";
        
        // Check products by category
        echo "<h4>📊 Products by Category</h4>";
        $stmt = $conn->query("
            SELECT c.name as category_name, COUNT(p.product_id) as product_count
            FROM categories c
            LEFT JOIN products p ON c.category_id = p.category_id AND p.is_active = 1
            GROUP BY c.category_id, c.name
            ORDER BY product_count DESC
        ");
        $categoryStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Category</th><th>Product Count</th></tr>";
        foreach ($categoryStats as $stat) {
            echo "<tr>";
            echo "<td>{$stat['category_name']}</td>";
            echo "<td>{$stat['product_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check if product_reviews table exists
    if (in_array('product_reviews', $tables)) {
        echo "<h3>⭐ Product Reviews Table</h3>";
        $stmt = $conn->query("SELECT COUNT(*) as total FROM product_reviews");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p class='info'>Total reviews: {$result['total']}</p>";
    } else {
        echo "<h3>⭐ Product Reviews Table</h3>";
        echo "<p class='error'>❌ product_reviews table does not exist</p>";
        
        // Create the table
        echo "<p class='info'>Creating product_reviews table...</p>";
        $createReviewsSQL = "
        CREATE TABLE product_reviews (
            review_id INT AUTO_INCREMENT PRIMARY KEY,
            product_id INT NOT NULL,
            user_id INT NOT NULL,
            rating TINYINT NOT NULL CHECK (rating BETWEEN 1 AND 5),
            review_title VARCHAR(100),
            review_text TEXT,
            is_approved BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(product_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )";
        
        try {
            $conn->exec($createReviewsSQL);
            echo "<p class='success'>✅ Created product_reviews table</p>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Failed to create product_reviews table: " . $e->getMessage() . "</p>";
        }
    }
    
    // Test the category filtering query
    echo "<h3>🧪 Test Category Filtering Query</h3>";
    $testCategoryId = 1; // Test with first category
    
    $stmt = $conn->prepare("
        SELECT p.product_id, p.name, c.name as category_name, p.price
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE p.is_active = 1 AND p.category_id = ?
        LIMIT 5
    ");
    $stmt->execute([$testCategoryId]);
    $filteredProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($filteredProducts)) {
        echo "<p class='success'>✅ Category filtering query works! Found " . count($filteredProducts) . " products for category ID $testCategoryId:</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Name</th><th>Category</th><th>Price</th></tr>";
        foreach ($filteredProducts as $product) {
            echo "<tr>";
            echo "<td>{$product['product_id']}</td>";
            echo "<td>" . substr($product['name'], 0, 40) . "...</td>";
            echo "<td>{$product['category_name']}</td>";
            echo "<td>Rp " . number_format($product['price'], 0, ',', '.') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ No products found for category ID $testCategoryId</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Quick Links</h3>";
echo "<ul>";
echo "<li><a href='products_public.php'>View Products Page</a></li>";
echo "<li><a href='products_public.php?category=1'>Test Category 1 Filter</a></li>";
echo "<li><a href='products_public.php?category=2'>Test Category 2 Filter</a></li>";
echo "<li><a href='create_missing_view.php'>Recreate Database View</a></li>";
echo "</ul>";
?>
