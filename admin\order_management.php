<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set admin session if not set (for testing)
if (!isset($_SESSION['user_role'])) {
    $_SESSION['user_role'] = 'admin';
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'admin';
}

try {
    require_once '../includes/db_connect.php';
    require_once '../includes/order_status_functions.php';
} catch (Exception $e) {
    die("Error loading includes: " . $e->getMessage());
}

// Check if user is admin
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_status') {
    $order_id = (int)$_POST['order_id'];
    $new_status = $_POST['status'];
    $notes = $_POST['notes'] ?? '';
    $tracking_number = $_POST['tracking_number'] ?? null;
    $estimated_delivery = $_POST['estimated_delivery'] ?? null;
    
    try {
        require_once '../includes/order_management_system.php';

        // Use the comprehensive order management system
        if (updateOrderStatusComplete($order_id, $new_status, $notes, $tracking_number, $conn)) {
            $_SESSION['alert_message'] = 'Status pesanan berhasil diperbarui dengan sistem lengkap';
            $_SESSION['alert_type'] = 'success';

            // Store update for real-time sync
            $_SESSION['real_time_sync'] = [
                'order_id' => $order_id,
                'new_status' => $new_status,
                'admin_note' => $notes,
                'tracking_number' => $tracking_number,
                'estimated_delivery' => $estimated_delivery,
                'timestamp' => time() * 1000,
                'admin_name' => 'Admin'
            ];
        } else {
            $_SESSION['alert_message'] = 'Gagal memperbarui status pesanan';
            $_SESSION['alert_type'] = 'danger';
        }
    } catch (Exception $e) {
        $_SESSION['alert_message'] = 'Error: ' . $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
        error_log("Order status update error: " . $e->getMessage());
    }
    
    header('Location: order_management.php');
    exit;
}

// Get filter parameters
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';

// Handle create sample data
if (isset($_GET['create_sample']) && $_GET['create_sample'] === '1') {
    try {
        // Create sample orders
        $sample_orders = [
            [
                'user_id' => 1,
                'total_amount' => 150000,
                'payment_method' => 'bank_transfer',
                'current_status' => 'pending',
                'order_date' => date('Y-m-d H:i:s'),
                'shipping_address' => 'Jl. Contoh No. 123, Jakarta',
                'phone' => '************'
            ],
            [
                'user_id' => 1,
                'total_amount' => 250000,
                'payment_method' => 'credit_card',
                'current_status' => 'confirmed',
                'order_date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'shipping_address' => 'Jl. Sample No. 456, Bandung',
                'phone' => '************'
            ],
            [
                'user_id' => 1,
                'total_amount' => 350000,
                'payment_method' => 'e_wallet',
                'current_status' => 'processing',
                'order_date' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'shipping_address' => 'Jl. Test No. 789, Surabaya',
                'phone' => '************'
            ]
        ];

        $created_count = 0;
        foreach ($sample_orders as $order) {
            // Generate order number
            $order_number = 'TW' . date('Ymd') . sprintf('%04d', rand(1000, 9999));

            $sql = "INSERT INTO orders (order_number, user_id, total_amount, payment_method, STATUS, current_status, order_date, shipping_address, shipping_phone)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            if ($stmt->execute([
                $order_number,
                $order['user_id'],
                $order['total_amount'],
                $order['payment_method'],
                $order['current_status'],
                $order['current_status'], // Set both STATUS and current_status
                $order['order_date'],
                $order['shipping_address'],
                $order['phone']
            ])) {
                $created_count++;
            }
        }

        $_SESSION['alert_message'] = "Successfully created {$created_count} sample orders!";
        $_SESSION['alert_type'] = 'success';
        header('Location: order_management.php');
        exit;

    } catch (Exception $e) {
        $_SESSION['alert_message'] = 'Error creating sample data: ' . $e->getMessage();
        $_SESSION['alert_type'] = 'danger';
    }
}

// Get orders
try {
    $orders = getOrdersByStatus($conn, $status_filter ?: null);
    $statuses = getOrderStatuses();

    // Filter by search if provided
    if ($search) {
        $orders = array_filter($orders, function($order) use ($search) {
            return stripos((string)($order['order_id'] ?? ''), $search) !== false ||
                   stripos((string)($order['customer_name'] ?? ''), $search) !== false ||
                   stripos((string)($order['customer_email'] ?? ''), $search) !== false ||
                   stripos((string)($order['tracking_number'] ?? ''), $search) !== false ||
                   stripos((string)($order['order_number'] ?? ''), $search) !== false;
        });
    }
} catch (Exception $e) {
    $_SESSION['alert_message'] = 'Error loading orders: ' . $e->getMessage();
    $_SESSION['alert_type'] = 'danger';
    $orders = [];
    $statuses = [];
}

$page = 'order_management';
$page_title = 'Order Management';
require_once 'includes/header.php';
?>

<!-- Enhanced Order Status CSS -->
<link href="../css/real-time-order-sync.css" rel="stylesheet">
<link href="../css/dynamic-status-logo.css" rel="stylesheet">

<div class="container-fluid py-4" id="alertContainer">
    <!-- Alert Messages -->
    <?php if (isset($_SESSION['alert_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['alert_type']; ?> alert-dismissible fade show" role="alert">
            <?php
            echo $_SESSION['alert_message'];
            unset($_SESSION['alert_message'], $_SESSION['alert_type']);
            ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-cart me-2"></i>Order Management</h2>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" onclick="refreshOrders()">
                <i class="fas fa-sync-alt me-1"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">Filter by Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Status</option>
                        <?php foreach ($statuses as $key => $status): ?>
                            <option value="<?php echo $key; ?>" <?php echo $status_filter === $key ? 'selected' : ''; ?>>
                                <?php echo $status['label']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">Search</label>
                    <input type="text" name="search" class="form-control" placeholder="Order ID, Customer name, Email..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                        <a href="order_management.php" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Status Summary Cards -->
    <div class="row mb-4">
        <?php
        $status_counts = [];
        foreach ($statuses as $key => $status) {
            $count_orders = getOrdersByStatus($conn, $key, 1000);
            $status_counts[$key] = count($count_orders);
        }

        // Get today's statistics
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE DATE(order_date) = CURDATE()");
            $today_orders = $stmt->fetch()['count'];

            $stmt = $conn->query("SELECT SUM(total_amount) as revenue FROM orders WHERE DATE(order_date) = CURDATE()");
            $today_revenue = $stmt->fetch()['revenue'] ?? 0;

            $stmt = $conn->query("SELECT COUNT(*) as count FROM orders WHERE STATUS IN ('pending', 'confirmed')");
            $pending_orders = $stmt->fetch()['count'];
        } catch (Exception $e) {
            $today_orders = 0;
            $today_revenue = 0;
            $pending_orders = 0;
        }
        ?>

        <!-- Today's Orders -->
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day fa-2x text-primary mb-2"></i>
                    <h4 class="text-primary today-orders-count"><?php echo $today_orders; ?></h4>
                    <p class="card-text">Today's Orders</p>
                </div>
            </div>
        </div>

        <!-- Today's Revenue -->
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <i class="fas fa-money-bill-wave fa-2x text-success mb-2"></i>
                    <h4 class="text-success today-revenue-amount">Rp <?php echo number_format($today_revenue, 0, ',', '.'); ?></h4>
                    <p class="card-text">Today's Revenue</p>
                </div>
            </div>
        </div>

        <!-- Pending Orders -->
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h4 class="text-warning pending-orders-count"><?php echo $pending_orders; ?></h4>
                    <p class="card-text">Pending Orders</p>
                </div>
            </div>
        </div>

        <!-- Total Orders -->
        <div class="col-md-3">
            <div class="card border-info">
                <div class="card-body text-center">
                    <i class="fas fa-shopping-cart fa-2x text-info mb-2"></i>
                    <h4 class="text-info"><?php echo count($orders); ?></h4>
                    <p class="card-text">Total Orders</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Breakdown -->
    <div class="row mb-4">
        <?php
        $summary_statuses = [
            ORDER_STATUS_PENDING => 'warning',
            ORDER_STATUS_PROCESSING => 'primary',
            ORDER_STATUS_SHIPPED => 'info',
            ORDER_STATUS_DELIVERED => 'success'
        ];

        foreach ($summary_statuses as $status_key => $color):
            $count = $status_counts[$status_key] ?? 0;
            $status_info = $statuses[$status_key];
        ?>
        <div class="col-md-3">
            <div class="card border-<?php echo $color; ?>" data-status="<?php echo $status_key; ?>">
                <div class="card-body text-center">
                    <i class="<?php echo $status_info['icon']; ?> fa-2x text-<?php echo $color; ?> mb-2"></i>
                    <h4 class="text-<?php echo $color; ?> stat-count"><?php echo $count; ?></h4>
                    <p class="card-text"><?php echo $status_info['label']; ?></p>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Bulk Actions -->
    <div id="bulkActions" class="alert alert-info" style="display: none;">
        <div class="d-flex justify-content-between align-items-center">
            <span><strong><span id="selectedCount">0</span></strong> orders selected</span>
            <div>
                <button class="btn btn-sm btn-primary btn-bulk-update">
                    <i class="fas fa-edit me-1"></i>Bulk Update Status
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('selectAllOrders').checked = false; orderManagement.selectAllOrders(false);">
                    <i class="fas fa-times me-1"></i>Clear Selection
                </button>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Orders List
                    <span class="badge bg-primary ms-2"><?php echo count($orders); ?> orders</span>
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshOrders()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="window.print()">
                        <i class="fas fa-print me-1"></i>Print
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (count($orders) > 0): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAllOrders" class="form-check-input">
                                </th>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Order Date</th>
                                <th>Tracking</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                                <tr data-order-id="<?php echo $order['order_id']; ?>">
                                    <td>
                                        <input type="checkbox" class="form-check-input order-checkbox" value="<?php echo $order['order_id']; ?>">
                                    </td>
                                    <td>
                                        <strong>#<?php echo $order['order_id']; ?></strong>
                                        <?php if (isset($order['order_number']) && $order['order_number']): ?>
                                            <br><small class="text-muted"><?php echo $order['order_number']; ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($order['customer_name'] ?? 'N/A'); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_email'] ?? ''); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>Rp <?php echo number_format($order['total_amount'] ?? 0, 0, ',', '.'); ?></strong>
                                        <br><small class="text-muted"><?php echo ucfirst($order['payment_method'] ?? 'N/A'); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo ($order['status_info']['color'] ?? 'secondary'); ?> status-badge" data-status="<?php echo ($order['current_status'] ?? 'pending'); ?>">
                                            <span class="status-logo"><?php echo $order['status_info']['logo'] ?? '⏰'; ?></span>
                                            <i class="<?php echo ($order['status_info']['icon'] ?? 'fas fa-clock'); ?> me-1"></i>
                                            <?php echo ($order['status_info']['label'] ?? 'Pending'); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo date('d M Y H:i', strtotime($order['order_date'] ?? 'now')); ?>
                                        <?php if (isset($order['estimated_delivery']) && $order['estimated_delivery']): ?>
                                            <br><small class="text-muted">Est: <?php echo date('d M Y', strtotime($order['estimated_delivery'])); ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="tracking-number">
                                        <?php if (isset($order['tracking_number']) && $order['tracking_number']): ?>
                                            <code><?php echo $order['tracking_number']; ?></code>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewOrder(<?php echo $order['order_id']; ?>)" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="updateStatus(<?php echo $order['order_id']; ?>)" title="Update Status">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if (!isset($order['tracking_number']) || !$order['tracking_number']): ?>
                                            <button class="btn btn-sm btn-outline-info btn-generate-tracking" data-order-id="<?php echo $order['order_id']; ?>" title="Generate Tracking">
                                                <i class="fas fa-barcode"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button class="btn btn-sm btn-outline-warning btn-notify-customer" data-order-id="<?php echo $order['order_id']; ?>" title="Notify Customer">
                                                <i class="fas fa-bell"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5>No orders found</h5>
                    <p class="text-muted">No orders match your current filters.</p>

                    <!-- Debug Information -->
                    <div class="mt-4 p-3 bg-light rounded">
                        <h6>Debug Information:</h6>
                        <p><strong>Status Filter:</strong> <?php echo $status_filter ?: 'None'; ?></p>
                        <p><strong>Search Query:</strong> <?php echo $search ?: 'None'; ?></p>
                        <p><strong>Total Orders in DB:</strong>
                            <?php
                            try {
                                $stmt = $conn->query("SELECT COUNT(*) as count FROM orders");
                                echo $stmt->fetch()['count'];
                            } catch (Exception $e) {
                                echo "Error: " . $e->getMessage();
                            }
                            ?>
                        </p>
                        <div class="mt-3">
                            <a href="?create_sample=1" class="btn btn-sm btn-success" onclick="return confirm('Create sample orders for testing?')">
                                <i class="fas fa-plus me-1"></i>Create Sample Orders
                            </a>
                            <a href="check_orders_data.php" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-bug me-1"></i>Check Database
                            </a>
                            <a href="?clear=1" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-refresh me-1"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="order_id" id="updateOrderId">
                    
                    <div class="mb-3">
                        <label class="form-label">New Status</label>
                        <select name="status" class="form-select" required>
                            <?php foreach ($statuses as $key => $status): ?>
                                <option value="<?php echo $key; ?>">
                                    <?php echo $status['label']; ?> - <?php echo $status['description']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Tracking Number (Optional)</label>
                        <input type="text" name="tracking_number" class="form-control" placeholder="Enter tracking number">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Estimated Delivery (Optional)</label>
                        <input type="date" name="estimated_delivery" class="form-control">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Notes (Optional)</label>
                        <textarea name="notes" class="form-control" rows="3" placeholder="Add notes for customer..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Order Modal -->
<div class="modal fade" id="viewOrderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Order Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailsContent">
                <!-- Content will be loaded via AJAX -->
            </div>
        </div>
    </div>
</div>

<!-- Real-time Order Status Sync -->
<script src="../js/real-time-status-sync.js"></script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/order-management.js"></script>
<script>
function updateStatus(orderId) {
    document.getElementById('updateOrderId').value = orderId;
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

function viewOrder(orderId) {
    // Load order details via AJAX
    fetch(`order_details.php?id=${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('orderDetailsContent').innerHTML = html;
            new bootstrap.Modal(document.getElementById('viewOrderModal')).show();
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            alert('Error loading order details');
        });
}

function refreshOrders() {
    window.location.reload();
}

// Enhanced admin order management with real-time sync
document.addEventListener('DOMContentLoaded', function() {
    // Handle real-time sync data from session
    <?php if (isset($_SESSION['real_time_sync'])): ?>
    const syncData = <?php echo json_encode($_SESSION['real_time_sync']); ?>;
    if (window.realTimeOrderSync && syncData) {
        window.realTimeOrderSync.storeAdminUpdate(syncData.order_id, {
            new_status: syncData.new_status,
            admin_note: syncData.admin_note,
            tracking_number: syncData.tracking_number,
            estimated_delivery: syncData.estimated_delivery,
            timestamp: syncData.timestamp,
            admin_name: syncData.admin_name
        });
        console.log('Real-time sync data processed for order:', syncData.order_id);
    }
    <?php unset($_SESSION['real_time_sync']); ?>
    <?php endif; ?>

    // Listen for form submissions to trigger real-time sync
    const updateForm = document.querySelector('form[method="POST"]');
    if (updateForm) {
        updateForm.addEventListener('submit', function(e) {
            const formData = new FormData(this);
            const orderId = formData.get('order_id');
            const newStatus = formData.get('status');
            const notes = formData.get('notes');
            const trackingNumber = formData.get('tracking_number');
            const estimatedDelivery = formData.get('estimated_delivery');

            // Store update for real-time sync
            if (window.realTimeOrderSync) {
                window.realTimeOrderSync.storeAdminUpdate(orderId, {
                    new_status: newStatus,
                    admin_note: notes,
                    tracking_number: trackingNumber,
                    estimated_delivery: estimatedDelivery,
                    timestamp: Date.now(),
                    admin_name: 'Admin'
                });
            }
        });
    }
});

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if no modal is open
    if (!document.querySelector('.modal.show')) {
        refreshOrders();
    }
}, 30000);
</script>

<?php require_once 'includes/footer.php'; ?>
