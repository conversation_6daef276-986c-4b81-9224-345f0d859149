<?php
/**
 * Create Missing Database View
 * This script creates the products_with_stats view that's referenced in the code
 */

require_once 'config.php';
require_once 'includes/db_connect.php';

echo "<h1>🔧 Creating Missing Database View</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: #28a745; }
.error { color: #dc3545; }
.info { color: #17a2b8; }
</style>";

try {
    // First, check if the view already exists
    $stmt = $conn->prepare("SHOW TABLES LIKE 'products_with_stats'");
    $stmt->execute();
    $viewExists = $stmt->fetch();
    
    if ($viewExists) {
        echo "<p class='info'>ℹ️ View 'products_with_stats' already exists. Dropping it first...</p>";
        $conn->exec("DROP VIEW products_with_stats");
    }
    
    // Create the products_with_stats view
    $createViewSQL = "
    CREATE VIEW products_with_stats AS
    SELECT 
        p.product_id,
        p.name,
        p.description,
        p.price,
        p.stock,
        p.image,
        p.category_id,
        p.is_active,
        p.created_at,
        c.name as category_name,
        COALESCE(AVG(pr.rating), 0) as rating,
        COUNT(DISTINCT pr.review_id) as review_count
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.category_id
    LEFT JOIN product_reviews pr ON p.product_id = pr.product_id
    WHERE p.is_active = 1
    GROUP BY p.product_id, p.name, p.description, p.price, p.stock, p.image, 
             p.category_id, p.is_active, p.created_at, c.name
    ";
    
    $conn->exec($createViewSQL);
    echo "<p class='success'>✅ Successfully created 'products_with_stats' view!</p>";
    
    // Test the view
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM products_with_stats");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p class='success'>✅ View contains {$result['total']} products</p>";
    
    // Show sample data from the view
    $stmt = $conn->prepare("SELECT product_id, name, category_name, price, rating, review_count FROM products_with_stats LIMIT 5");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($products)) {
        echo "<h3>📋 Sample Data from View:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Name</th><th>Category</th><th>Price</th><th>Rating</th><th>Reviews</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>{$product['product_id']}</td>";
            echo "<td>{$product['name']}</td>";
            echo "<td>{$product['category_name'] ?? 'No Category'}</td>";
            echo "<td>Rp " . number_format($product['price'], 0, ',', '.') . "</td>";
            echo "<td>" . number_format($product['rating'], 1) . "</td>";
            echo "<td>{$product['review_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error creating view: " . $e->getMessage() . "</p>";
    
    // Try to create a simpler version without reviews
    try {
        echo "<p class='info'>ℹ️ Trying to create simplified view without reviews...</p>";
        
        $simpleViewSQL = "
        CREATE VIEW products_with_stats AS
        SELECT 
            p.product_id,
            p.name,
            p.description,
            p.price,
            p.stock,
            p.image,
            p.category_id,
            p.is_active,
            p.created_at,
            c.name as category_name,
            0 as rating,
            0 as review_count
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.category_id
        WHERE p.is_active = 1
        ";
        
        $conn->exec($simpleViewSQL);
        echo "<p class='success'>✅ Successfully created simplified 'products_with_stats' view!</p>";
        
        // Test the simplified view
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM products_with_stats");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p class='success'>✅ Simplified view contains {$result['total']} products</p>";
        
    } catch (Exception $e2) {
        echo "<p class='error'>❌ Failed to create simplified view: " . $e2->getMessage() . "</p>";
    }
}

echo "<hr>";
echo "<h3>🔄 Next Steps:</h3>";
echo "<ul>";
echo "<li><a href='test_category_filtering.php'>Run Category Filtering Test</a></li>";
echo "<li><a href='products_public.php'>View Products Page</a></li>";
echo "<li><a href='products_public.php?category=1'>Test Category Filter (Cosmetics)</a></li>";
echo "</ul>";
?>
