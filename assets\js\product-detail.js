document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Product detail page loaded');
    console.log('Current URL:', window.location.href);
    console.log('DOM ready state:', document.readyState);

    // Check if universal handler is available
    if (window.universalProductHandler) {
        console.log('✅ Universal handler detected, product-detail.js will use it');
        // Add visual indicator that universal handler is working
        addUniversalHandlerIndicator();
        return;
    }

    console.log('⚠️ Universal handler not found, using legacy product-detail.js');

    // Get elements
    const quantityInput = document.getElementById('quantity');
    const decreaseBtn = document.getElementById('decrease-quantity');
    const increaseBtn = document.getElementById('increase-quantity');
    const alertContainer = document.getElementById('alert-container');
    const spinnerElement = document.getElementById('spinner');
    const addToCartBtn = document.querySelector('.add-to-cart-btn');

    console.log('Elements found:', {
        quantityInput: !!quantityInput,
        decreaseBtn: !!decreaseBtn,
        increaseBtn: !!increaseBtn,
        alertContainer: !!alertContainer,
        spinnerElement: !!spinnerElement,
        addToCartBtn: !!addToCartBtn
    });

    // Stop event propagation to prevent conflicts with other scripts
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            e.stopImmediatePropagation();
        }, true);
    }

    // Helper functions
    function showSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'block';
        }
    }

    function hideSpinner() {
        if (spinnerElement) {
            spinnerElement.style.display = 'none';
        }
    }

    function showAlert(message, type = 'success') {
        if (!alertContainer) return;

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        alertContainer.innerHTML = alertHtml;

        // Automatically dismiss alert after 5 seconds
        setTimeout(() => {
            const alert = alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    if (quantityInput && decreaseBtn && increaseBtn) {
        console.log('Setting up quantity controls');

        // Update quantity - decrease
        decreaseBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            let value = parseInt(quantityInput.value) || 1;
            console.log('Decrease clicked, current value:', value);

            if (value > 1) {
                quantityInput.value = value - 1;
                console.log('New value:', quantityInput.value);

                // Add visual feedback
                quantityInput.classList.add('quantity-changed');
                setTimeout(() => quantityInput.classList.remove('quantity-changed'), 300);

                // Trigger change event
                quantityInput.dispatchEvent(new Event('change'));
            } else {
                showAlert('Minimum quantity is 1', 'warning');
            }
        });

        // Update quantity - increase
        increaseBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            let value = parseInt(quantityInput.value) || 1;
            let max = parseInt(quantityInput.getAttribute('max')) || 999;
            console.log('Increase clicked, current value:', value, 'max:', max);

            if (value < max) {
                quantityInput.value = value + 1;
                console.log('New value:', quantityInput.value);

                // Add visual feedback
                quantityInput.classList.add('quantity-changed');
                setTimeout(() => quantityInput.classList.remove('quantity-changed'), 300);

                // Trigger change event
                quantityInput.dispatchEvent(new Event('change'));
            } else {
                // Show max stock message
                showAlert(`Maximum stock available: ${max}`, 'warning');
            }
        });

        // Validate quantity input
        quantityInput.addEventListener('change', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            let min = parseInt(quantityInput.getAttribute('min')) || 1;

            if (isNaN(value) || value < min) {
                quantityInput.value = min;
                showAlert(`Minimum quantity is ${min}`, 'warning');
            } else if (value > max) {
                quantityInput.value = max;
                showAlert(`Maximum stock available: ${max}`, 'warning');
            }
        });

        // Add input validation on keyup for real-time feedback
        quantityInput.addEventListener('input', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.getAttribute('max'));
            let min = parseInt(quantityInput.getAttribute('min')) || 1;

            // Remove non-numeric characters
            quantityInput.value = quantityInput.value.replace(/[^0-9]/g, '');

            // Update button states
            decreaseBtn.disabled = value <= min;
            increaseBtn.disabled = value >= max;
        });
    }

    // Handle add to cart button for product detail page
    if (addToCartBtn) {
        console.log('Setting up add to cart event listener');

        // Remove any existing event listeners
        addToCartBtn.removeEventListener('click', handleAddToCart);

        // Add new event listener
        addToCartBtn.addEventListener('click', handleAddToCart);
    }

    function handleAddToCart(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();

        console.log('Product detail add to cart clicked');

        const button = this;

        const productId = button.dataset.productId;
        let quantity = 1;

        if (quantityInput) {
            quantity = parseInt(quantityInput.value) || 1;
            const maxStock = parseInt(quantityInput.getAttribute('max'));

            // Validate quantity against stock
            if (quantity > maxStock) {
                showAlert(`Only ${maxStock} items available in stock`, 'warning');
                quantityInput.value = maxStock;
                quantity = maxStock;
            }

            if (quantity < 1) {
                showAlert('Quantity must be at least 1', 'warning');
                quantityInput.value = 1;
                quantity = 1;
            }
        } else {
            console.warn('Quantity input not found, using default quantity of 1');
        }

        console.log('Product ID:', productId, 'Quantity:', quantity);

        // Prevent double clicks
        if (button.disabled) {
            console.log('Button already disabled, ignoring click');
            return;
        }

        // Set loading state
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';

        showSpinner();

        // Create form data
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        console.log('Sending add to cart request:', {
            product_id: productId,
            quantity: quantity,
            url: 'ajax/simple_add_to_cart.php'
        });

        // Send request to add to cart
        fetch('ajax/simple_add_to_cart.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Add to cart response:', data);
                hideSpinner();

            if (data.success) {
                // Success
                button.innerHTML = '<i class="fas fa-check"></i> Added!';
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');

                // Update cart count
                const cartCount = parseInt(data.cart_count) || 0;
                if (typeof updateCartCount === 'function') {
                    updateCartCount(cartCount);
                } else {
                    // Fallback cart count update
                    const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count');
                    cartElements.forEach(element => {
                        element.textContent = cartCount;
                        if (cartCount > 0) {
                            element.style.display = 'inline';
                        }
                    });
                }

                // Show success notification with product details and view cart option
                const successMsg = `${data.product_name || 'Product'} (${data.quantity || quantity}) added to cart!`;
                showAlert(successMsg, 'success');

                // Add a temporary "View Cart" button
                const viewCartBtn = document.createElement('a');
                viewCartBtn.href = 'cart.php';
                viewCartBtn.className = 'btn btn-outline-primary btn-sm ms-2';
                viewCartBtn.innerHTML = '<i class="fas fa-eye me-1"></i>View Cart';
                button.parentNode.appendChild(viewCartBtn);

                // Reset button after 3 seconds
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-primary');
                    button.disabled = false;
                    // Remove view cart button
                    if (viewCartBtn.parentNode) {
                        viewCartBtn.remove();
                    }
                }, 3000);
            } else {
                // Error
                console.error('Add to cart failed:', data.message);
                button.innerHTML = originalText;
                button.disabled = false;
                showAlert(data.message || 'Failed to add product to cart', 'danger');
            }
            })
        .catch(error => {
            console.error('Network error:', error);
            hideSpinner();
            button.innerHTML = originalText;
            button.disabled = false;
            showAlert('Network error. Please try again.', 'danger');
        });
    }

    // Add CSS for quantity animations and button fixes
    const style = document.createElement('style');
    style.textContent = `
        .quantity-changed {
            background-color: #e3f2fd !important;
            transition: background-color 0.3s ease;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .product-detail-loaded {
            border-left: 3px solid #28a745;
            padding-left: 10px;
        }

        /* Ensure buttons are clickable */
        #decrease-quantity, #increase-quantity, .add-to-cart-btn {
            cursor: pointer !important;
            pointer-events: auto !important;
            position: relative !important;
            z-index: 10 !important;
        }

        /* Fix button hover states */
        #decrease-quantity:hover, #increase-quantity:hover {
            background-color: #6c757d !important;
            border-color: #6c757d !important;
            color: white !important;
        }

        .add-to-cart-btn:hover {
            background-color: #0056b3 !important;
            border-color: #0056b3 !important;
        }

        /* Ensure input group buttons work */
        .input-group .btn {
            border-radius: 0 !important;
        }

        .input-group .btn:first-child {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .input-group .btn:last-child {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
        }
    `;
    document.head.appendChild(style);

    // Add a visual indicator that JavaScript is working
    const productDetail = document.querySelector('.product-detail');
    if (productDetail) {
        productDetail.classList.add('product-detail-loaded');
        const indicator = document.createElement('div');
        indicator.style.cssText = 'color: green; font-size: 12px; margin-top: 10px; padding: 5px; background: #e8f5e8; border-radius: 3px;';
        indicator.innerHTML = '✅ JavaScript loaded and ready<br>🔧 Buttons should be working now';
        productDetail.appendChild(indicator);

        // Add test buttons for debugging
        const testContainer = document.createElement('div');
        testContainer.className = 'mt-2';

        const testBtn = document.createElement('button');
        testBtn.className = 'btn btn-info btn-sm me-2';
        testBtn.textContent = 'Test JS';
        testBtn.onclick = function() {
            alert('JavaScript is working! Buttons should be responsive now.');
        };

        const testQuantityBtn = document.createElement('button');
        testQuantityBtn.className = 'btn btn-warning btn-sm me-2';
        testQuantityBtn.textContent = 'Test Quantity';
        testQuantityBtn.onclick = function() {
            if (quantityInput) {
                quantityInput.value = Math.floor(Math.random() * 5) + 1;
                quantityInput.dispatchEvent(new Event('change'));
                alert('Quantity set to: ' + quantityInput.value);
            } else {
                alert('Quantity input not found!');
            }
        };

        const testCartBtn = document.createElement('button');
        testCartBtn.className = 'btn btn-success btn-sm';
        testCartBtn.textContent = 'Test Add to Cart';
        testCartBtn.onclick = function() {
            if (addToCartBtn) {
                addToCartBtn.click();
            } else {
                alert('Add to cart button not found!');
            }
        };

        testContainer.appendChild(testBtn);
        testContainer.appendChild(testQuantityBtn);
        testContainer.appendChild(testCartBtn);
        indicator.appendChild(testContainer);
    }

    // Force a small delay to ensure all elements are ready
    setTimeout(() => {
        console.log('Final check - all elements ready');
        console.log('Quantity input:', !!quantityInput);
        console.log('Decrease button:', !!decreaseBtn);
        console.log('Increase button:', !!increaseBtn);
        console.log('Add to cart button:', !!addToCartBtn);
    }, 100);
});

// Function to add universal handler indicator
function addUniversalHandlerIndicator() {
    const productDetail = document.querySelector('.product-detail');
    if (productDetail) {
        const indicator = document.createElement('div');
        indicator.style.cssText = 'color: green; font-size: 12px; margin-top: 10px; padding: 5px; background: #e8f5e8; border-radius: 3px;';
        indicator.innerHTML = '🌟 Universal Product Handler Active<br>✅ All buttons should be working properly';
        productDetail.appendChild(indicator);

        // Add test button for debugging
        const testBtn = document.createElement('button');
        testBtn.className = 'btn btn-info btn-sm mt-2';
        testBtn.textContent = 'Test Universal Handler';
        testBtn.onclick = function() {
            if (window.universalProductHandler) {
                window.universalProductHandler.showNotification('Universal handler is working!', 'success');
            } else {
                alert('Universal handler not found!');
            }
        };
        indicator.appendChild(testBtn);
    }
}
