/**
 * Enhanced Product Detail Page Handler
 * Handles all buttons and interactions on product detail page
 */

document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Enhanced Product Detail Page loaded');
    console.log('Current URL:', window.location.href);
    console.log('DOM ready state:', document.readyState);

    // Initialize enhanced product detail handler
    const productDetailHandler = new EnhancedProductDetailHandler();

    // Make it globally available
    window.productDetailHandler = productDetailHandler;

    console.log('✅ Enhanced Product Detail Handler initialized');
});

/**
 * Enhanced Product Detail Handler Class
 */
class EnhancedProductDetailHandler {
    constructor() {
        this.init();
    }

    init() {
        // Get all elements
        this.getElements();

        // Setup all handlers
        this.setupQuantityControls();
        this.setupAddToCartButton();
        this.setupWishlistButton();
        this.setupShareButton();
        this.setupKeyboardShortcuts();

        // Add enhanced styling
        this.addEnhancedStyles();

        // Add visual indicators
        this.addVisualIndicators();

        console.log('🎯 All product detail handlers ready');
    }

    getElements() {
        // Quantity controls
        this.quantityInput = document.getElementById('quantity');
        this.decreaseBtn = document.getElementById('decrease-quantity');
        this.increaseBtn = document.getElementById('increase-quantity');

        // Action buttons
        this.addToCartBtn = document.querySelector('.add-to-cart-btn');
        this.wishlistBtn = document.querySelector('.wishlist-btn');
        this.shareBtn = document.querySelector('.share-btn');

        // Containers
        this.productDetail = document.querySelector('.product-detail');
        this.alertContainer = document.getElementById('alert-container');
        this.spinnerElement = document.getElementById('spinner');

        console.log('📋 Elements found:', {
            quantityInput: !!this.quantityInput,
            decreaseBtn: !!this.decreaseBtn,
            increaseBtn: !!this.increaseBtn,
            addToCartBtn: !!this.addToCartBtn,
            wishlistBtn: !!this.wishlistBtn,
            shareBtn: !!this.shareBtn,
            productDetail: !!this.productDetail
        });
    }

    setupQuantityControls() {
        if (!this.quantityInput || !this.decreaseBtn || !this.increaseBtn) {
            console.warn('⚠️ Quantity controls not found');
            return;
        }

        console.log('🔢 Setting up quantity controls');

        // Decrease quantity
        this.decreaseBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.decreaseQuantity();
        });

        // Increase quantity
        this.increaseBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.increaseQuantity();
        });

        // Validate input changes
        this.quantityInput.addEventListener('input', (e) => {
            this.validateQuantityInput();
        });

        this.quantityInput.addEventListener('change', (e) => {
            this.validateQuantityInput();
        });

        // Keyboard support
        this.quantityInput.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.increaseQuantity();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.decreaseQuantity();
            }
        });
    }

    decreaseQuantity() {
        const currentValue = parseInt(this.quantityInput.value) || 1;
        const minValue = parseInt(this.quantityInput.getAttribute('min')) || 1;

        if (currentValue > minValue) {
            this.quantityInput.value = currentValue - 1;
            this.addQuantityAnimation();
            this.updateButtonStates();
            console.log('📉 Quantity decreased to:', this.quantityInput.value);
        } else {
            this.showNotification(`Minimum quantity is ${minValue}`, 'warning');
            this.shakeElement(this.decreaseBtn);
        }
    }

    increaseQuantity() {
        const currentValue = parseInt(this.quantityInput.value) || 1;
        const maxValue = parseInt(this.quantityInput.getAttribute('max')) || 999;

        if (currentValue < maxValue) {
            this.quantityInput.value = currentValue + 1;
            this.addQuantityAnimation();
            this.updateButtonStates();
            console.log('📈 Quantity increased to:', this.quantityInput.value);
        } else {
            this.showNotification(`Maximum stock available: ${maxValue}`, 'warning');
            this.shakeElement(this.increaseBtn);
        }
    }

    validateQuantityInput() {
        const value = parseInt(this.quantityInput.value);
        const minValue = parseInt(this.quantityInput.getAttribute('min')) || 1;
        const maxValue = parseInt(this.quantityInput.getAttribute('max')) || 999;

        if (isNaN(value) || value < minValue) {
            this.quantityInput.value = minValue;
            this.showNotification(`Minimum quantity is ${minValue}`, 'warning');
        } else if (value > maxValue) {
            this.quantityInput.value = maxValue;
            this.showNotification(`Maximum stock available: ${maxValue}`, 'warning');
        }

        this.updateButtonStates();
    }

    updateButtonStates() {
        const value = parseInt(this.quantityInput.value) || 1;
        const minValue = parseInt(this.quantityInput.getAttribute('min')) || 1;
        const maxValue = parseInt(this.quantityInput.getAttribute('max')) || 999;

        // Update button states
        this.decreaseBtn.disabled = value <= minValue;
        this.increaseBtn.disabled = value >= maxValue;

        // Add visual feedback
        this.decreaseBtn.classList.toggle('btn-outline-secondary', !this.decreaseBtn.disabled);
        this.decreaseBtn.classList.toggle('btn-secondary', this.decreaseBtn.disabled);

        this.increaseBtn.classList.toggle('btn-outline-secondary', !this.increaseBtn.disabled);
        this.increaseBtn.classList.toggle('btn-secondary', this.increaseBtn.disabled);
    }

    addQuantityAnimation() {
        this.quantityInput.classList.add('quantity-changed');
        setTimeout(() => {
            this.quantityInput.classList.remove('quantity-changed');
        }, 300);
    }

    setupAddToCartButton() {
        if (!this.addToCartBtn) {
            console.warn('⚠️ Add to cart button not found');
            return;
        }

        console.log('🛒 Setting up add to cart button');

        this.addToCartBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleAddToCart();
        });
    }

    setupWishlistButton() {
        if (!this.wishlistBtn) {
            console.warn('⚠️ Wishlist button not found');
            return;
        }

        console.log('❤️ Setting up wishlist button');

        this.wishlistBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleWishlist();
        });
    }

    setupShareButton() {
        if (!this.shareBtn) {
            console.warn('⚠️ Share button not found');
            return;
        }

        console.log('📤 Setting up share button');

        this.shareBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.handleShare();
        });
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only if not typing in an input
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            switch(e.key.toLowerCase()) {
                case 'a':
                    if (e.ctrlKey || e.metaKey) return; // Don't interfere with Ctrl+A
                    e.preventDefault();
                    this.handleAddToCart();
                    break;
                case 'w':
                    e.preventDefault();
                    this.handleWishlist();
                    break;
                case 's':
                    if (e.ctrlKey || e.metaKey) return; // Don't interfere with Ctrl+S
                    e.preventDefault();
                    this.handleShare();
                    break;
                case '+':
                case '=':
                    e.preventDefault();
                    this.increaseQuantity();
                    break;
                case '-':
                    e.preventDefault();
                    this.decreaseQuantity();
                    break;
            }
        });
    }

    // Helper functions
    showSpinner() {
        if (this.spinnerElement) {
            this.spinnerElement.style.display = 'block';
        }
    }

    hideSpinner() {
        if (this.spinnerElement) {
            this.spinnerElement.style.display = 'none';
        }
    }

    showNotification(message, type = 'success') {
        if (!this.alertContainer) {
            // Fallback to console if no alert container
            console.log(`${type.toUpperCase()}: ${message}`);
            return;
        }

        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        this.alertContainer.innerHTML = alertHtml;

        // Automatically dismiss alert after 5 seconds
        setTimeout(() => {
            const alert = this.alertContainer.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'times-circle',
            'error': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    shakeElement(element) {
        element.classList.add('shake-animation');
        setTimeout(() => {
            element.classList.remove('shake-animation');
        }, 500);
    }

    handleAddToCart() {
        if (this.addToCartBtn.disabled) {
            console.log('Add to cart button is disabled, ignoring click');
            return;
        }

        console.log('🛒 Handling add to cart');

        const productId = this.addToCartBtn.dataset.productId;
        const quantity = parseInt(this.quantityInput?.value) || 1;

        if (!productId) {
            this.showNotification('Product ID not found', 'error');
            return;
        }

        // Validate quantity
        if (this.quantityInput) {
            const maxStock = parseInt(this.quantityInput.getAttribute('max'));
            if (quantity > maxStock) {
                this.showNotification(`Only ${maxStock} items available in stock`, 'warning');
                this.quantityInput.value = maxStock;
                return;
            }
        }

        // Set loading state
        const originalText = this.addToCartBtn.innerHTML;
        this.addToCartBtn.disabled = true;
        this.addToCartBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Adding...';

        this.showSpinner();

        // Send request
        const formData = new FormData();
        formData.append('product_id', productId);
        formData.append('quantity', quantity);

        fetch('ajax/simple_add_to_cart.php', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            this.hideSpinner();

            if (data.success) {
                // Success state
                this.addToCartBtn.innerHTML = '<i class="fas fa-check me-2"></i>Added!';
                this.addToCartBtn.classList.remove('btn-primary');
                this.addToCartBtn.classList.add('btn-success');

                // Update cart count
                this.updateCartCount(data.cart_count);

                // Show success notification
                const productName = this.addToCartBtn.dataset.productName || 'Product';
                this.showNotification(`${productName} (${quantity}) added to cart!`, 'success');

                // Add view cart button
                this.addViewCartButton();

                // Reset button after 3 seconds
                setTimeout(() => {
                    this.addToCartBtn.innerHTML = originalText;
                    this.addToCartBtn.classList.remove('btn-success');
                    this.addToCartBtn.classList.add('btn-primary');
                    this.addToCartBtn.disabled = false;
                    this.removeViewCartButton();
                }, 3000);

            } else {
                // Error state
                this.addToCartBtn.innerHTML = originalText;
                this.addToCartBtn.disabled = false;
                this.showNotification(data.message || 'Failed to add product to cart', 'error');
            }
        })
        .catch(error => {
            console.error('Add to cart error:', error);
            this.hideSpinner();
            this.addToCartBtn.innerHTML = originalText;
            this.addToCartBtn.disabled = false;
            this.showNotification('Network error. Please try again.', 'error');
        });
    }

    handleWishlist() {
        console.log('❤️ Handling wishlist');

        const productId = this.wishlistBtn.dataset.productId;

        if (!productId) {
            this.showNotification('Product ID not found', 'error');
            return;
        }

        // Toggle wishlist state
        const isInWishlist = this.wishlistBtn.classList.contains('btn-primary');

        if (isInWishlist) {
            // Remove from wishlist
            this.wishlistBtn.classList.remove('btn-primary');
            this.wishlistBtn.classList.add('btn-outline-primary');
            this.wishlistBtn.innerHTML = '<i class="fas fa-heart me-2"></i>Wishlist';
            this.showNotification('Removed from wishlist', 'info');
        } else {
            // Add to wishlist
            this.wishlistBtn.classList.remove('btn-outline-primary');
            this.wishlistBtn.classList.add('btn-primary');
            this.wishlistBtn.innerHTML = '<i class="fas fa-heart me-2"></i>In Wishlist';
            this.showNotification('Added to wishlist', 'success');
        }

        // Here you would typically send an AJAX request to update the wishlist
        // For now, we'll just show the visual feedback
    }

    handleShare() {
        console.log('📤 Handling share');

        const productName = this.shareBtn.dataset.productName || 'Product';
        const currentUrl = window.location.href;

        // Check if Web Share API is supported
        if (navigator.share) {
            navigator.share({
                title: productName,
                text: `Check out this product: ${productName}`,
                url: currentUrl
            })
            .then(() => {
                this.showNotification('Product shared successfully!', 'success');
            })
            .catch((error) => {
                console.log('Error sharing:', error);
                this.fallbackShare(currentUrl, productName);
            });
        } else {
            this.fallbackShare(currentUrl, productName);
        }
    }

    fallbackShare(url, productName) {
        // Copy to clipboard as fallback
        navigator.clipboard.writeText(url)
            .then(() => {
                this.showNotification('Product link copied to clipboard!', 'success');
            })
            .catch(() => {
                // Show share modal as final fallback
                this.showShareModal(url, productName);
            });
    }

    showShareModal(url, productName) {
        const shareOptions = [
            { name: 'WhatsApp', url: `https://wa.me/?text=${encodeURIComponent(productName + ' - ' + url)}`, icon: 'fab fa-whatsapp', color: '#25D366' },
            { name: 'Facebook', url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, icon: 'fab fa-facebook', color: '#1877F2' },
            { name: 'Twitter', url: `https://twitter.com/intent/tweet?text=${encodeURIComponent(productName)}&url=${encodeURIComponent(url)}`, icon: 'fab fa-twitter', color: '#1DA1F2' },
            { name: 'Copy Link', action: 'copy', icon: 'fas fa-copy', color: '#6c757d' }
        ];

        let modalHtml = `
            <div class="modal fade" id="shareModal" tabindex="-1">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Share Product</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="d-grid gap-2">
        `;

        shareOptions.forEach(option => {
            if (option.action === 'copy') {
                modalHtml += `
                    <button class="btn btn-outline-secondary" onclick="navigator.clipboard.writeText('${url}').then(() => { alert('Link copied!'); })">
                        <i class="${option.icon} me-2" style="color: ${option.color}"></i>${option.name}
                    </button>
                `;
            } else {
                modalHtml += `
                    <a href="${option.url}" target="_blank" class="btn btn-outline-secondary">
                        <i class="${option.icon} me-2" style="color: ${option.color}"></i>${option.name}
                    </a>
                `;
            }
        });

        modalHtml += `
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page and show it
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('shareModal'));
        modal.show();

        // Remove modal when hidden
        document.getElementById('shareModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    updateCartCount(count) {
        const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count, [data-cart-count]');
        cartElements.forEach(element => {
            element.textContent = count;
            if (count > 0) {
                element.style.display = 'inline';
                element.classList.add('cart-updated');
                setTimeout(() => element.classList.remove('cart-updated'), 1000);
            }
        });

        // Update cart icon if available
        const cartIcon = document.querySelector('.cart-icon');
        if (cartIcon && count > 0) {
            cartIcon.classList.add('cart-has-items');
        }
    }

    addViewCartButton() {
        if (this.viewCartBtn) return; // Already exists

        this.viewCartBtn = document.createElement('a');
        this.viewCartBtn.href = 'cart.php';
        this.viewCartBtn.className = 'btn btn-outline-primary btn-lg ms-2';
        this.viewCartBtn.innerHTML = '<i class="fas fa-eye me-2"></i>View Cart';
        this.addToCartBtn.parentNode.appendChild(this.viewCartBtn);
    }

    removeViewCartButton() {
        if (this.viewCartBtn && this.viewCartBtn.parentNode) {
            this.viewCartBtn.remove();
            this.viewCartBtn = null;
        }
    }

    addEnhancedStyles() {
        const style = document.createElement('style');
        style.textContent = `
            /* Enhanced Product Detail Styles */
            .quantity-changed {
                background-color: #e3f2fd !important;
                transform: scale(1.05);
                transition: all 0.3s ease;
            }

            .shake-animation {
                animation: shake 0.5s ease-in-out;
            }

            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            .cart-updated {
                animation: pulse 1s ease-in-out;
                color: #28a745 !important;
                font-weight: bold;
            }

            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.2); }
                100% { transform: scale(1); }
            }

            .cart-has-items {
                color: #28a745 !important;
            }

            /* Button enhancements */
            .btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .btn:not(:disabled):hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                transition: all 0.2s ease;
            }

            .btn:not(:disabled):active {
                transform: translateY(0);
            }

            /* Quantity controls */
            .quantity-selector .btn {
                min-width: 40px;
                height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .quantity-selector .form-control {
                height: 38px;
                border-left: 0;
                border-right: 0;
            }

            .quantity-selector .btn:first-child {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .quantity-selector .btn:last-child {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            /* Product detail enhancements */
            .product-detail-loaded {
                border-left: 3px solid #28a745;
                padding-left: 15px;
                background: linear-gradient(90deg, rgba(40,167,69,0.1) 0%, transparent 100%);
            }

            /* Alert enhancements */
            .alert {
                border-radius: 8px;
                border: none;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            /* Wishlist button states */
            .wishlist-btn.btn-primary {
                background-color: #dc3545;
                border-color: #dc3545;
            }

            .wishlist-btn.btn-primary:hover {
                background-color: #c82333;
                border-color: #bd2130;
            }

            /* Share button enhancement */
            .share-btn:hover {
                background-color: #6c757d;
                border-color: #6c757d;
                color: white;
            }

            /* Keyboard shortcut hints */
            .keyboard-hint {
                font-size: 0.75rem;
                color: #6c757d;
                margin-top: 5px;
            }
        `;
        document.head.appendChild(style);
    }

    addVisualIndicators() {
        if (!this.productDetail) return;

        this.productDetail.classList.add('product-detail-loaded');

        const indicator = document.createElement('div');
        indicator.className = 'alert alert-success mt-3';
        indicator.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle me-2"></i>
                <div>
                    <strong>Enhanced Product Detail Ready!</strong><br>
                    <small>All buttons are working with enhanced functionality</small>
                </div>
            </div>
            <div class="keyboard-hint mt-2">
                <strong>Keyboard shortcuts:</strong>
                A = Add to Cart | W = Wishlist | S = Share | +/- = Quantity
            </div>
        `;

        this.productDetail.appendChild(indicator);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            indicator.style.transition = 'opacity 0.5s ease';
            indicator.style.opacity = '0';
            setTimeout(() => indicator.remove(), 500);
        }, 5000);
    }
}

// Legacy support - if someone tries to call the old functions
window.showAlert = function(message, type) {
    if (window.productDetailHandler) {
        window.productDetailHandler.showNotification(message, type);
    } else {
        console.log(`${type}: ${message}`);
    }
};

window.updateCartCount = function(count) {
    if (window.productDetailHandler) {
        window.productDetailHandler.updateCartCount(count);
    }
};

// Export for global access
window.EnhancedProductDetailHandler = EnhancedProductDetailHandler;
