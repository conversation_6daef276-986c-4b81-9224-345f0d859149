<?php
/**
 * Comprehensive Order Management System
 * Connects all order-related functionality after order creation
 */

require_once __DIR__ . '/db_connect.php';
require_once __DIR__ . '/order_status_functions.php';
require_once __DIR__ . '/notification_functions.php';
require_once __DIR__ . '/enhanced_notification_functions.php';

/**
 * Complete Order Processing Pipeline
 * Handles everything that happens after an order is created
 */
class OrderManagementSystem {
    private $conn;
    
    public function __construct($database_connection) {
        $this->conn = $database_connection;
    }
    
    /**
     * Process new order - called immediately after order creation
     */
    public function processNewOrder($order_id, $user_id, $order_data = []) {
        try {
            // 1. Create initial order notification
            $this->createOrderWelcomeNotification($order_id, $user_id);
            
            // 2. Set up order tracking
            $this->initializeOrderTracking($order_id);
            
            // 3. Create order timeline entry
            $this->createOrderTimelineEntry($order_id, 'created', 'Order has been created and is pending confirmation');
            
            // 4. Send confirmation email (if email system is available)
            $this->sendOrderConfirmationEmail($order_id, $user_id);
            
            // 5. Initialize real-time sync data
            $this->initializeRealTimeSync($order_id, $user_id);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error processing new order {$order_id}: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update order status with full pipeline
     */
    public function updateOrderStatus($order_id, $new_status, $admin_notes = '', $tracking_number = null) {
        try {
            // Get current order info
            $stmt = $this->conn->prepare("SELECT * FROM orders WHERE order_id = ?");
            $stmt->execute([$order_id]);
            $order = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$order) {
                throw new Exception("Order not found");
            }
            
            $old_status = $order['current_status'] ?? $order['order_status'] ?? $order['status'] ?? 'pending';
            
            // 1. Update order status using the main function
            $result = updateOrderStatus($this->conn, $order_id, $new_status, $admin_notes, $tracking_number);
            
            if ($result) {
                // 2. Create timeline entry
                $this->createOrderTimelineEntry($order_id, $new_status, $admin_notes, $tracking_number);
                
                // 3. Send status-specific notifications
                $this->sendStatusSpecificNotifications($order_id, $order['user_id'], $old_status, $new_status, $tracking_number);
                
                // 4. Update real-time sync
                $this->updateRealTimeSync($order_id, $old_status, $new_status, $admin_notes, $tracking_number);
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Error updating order status: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Create welcome notification for new orders
     */
    private function createOrderWelcomeNotification($order_id, $user_id) {
        $title = "🎉 Order Received!";
        $message = "Thank you for your order! Your order #{$order_id} has been received and will be processed soon.";
        
        createOrderNotification($this->conn, $order_id, $user_id, 'pending', $message);
    }
    
    /**
     * Initialize order tracking
     */
    private function initializeOrderTracking($order_id) {
        try {
            // Create order_tracking table if it doesn't exist
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS order_tracking (
                    tracking_id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    location VARCHAR(255),
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE
                )
            ");
            
            // Insert initial tracking entry
            $stmt = $this->conn->prepare("
                INSERT INTO order_tracking (order_id, status, notes) 
                VALUES (?, 'created', 'Order has been created and is being processed')
            ");
            $stmt->execute([$order_id]);
            
        } catch (Exception $e) {
            error_log("Error initializing order tracking: " . $e->getMessage());
        }
    }
    
    /**
     * Create order timeline entry
     */
    private function createOrderTimelineEntry($order_id, $status, $notes = '', $tracking_number = null) {
        try {
            // Create order_timeline table if it doesn't exist
            $this->conn->exec("
                CREATE TABLE IF NOT EXISTS order_timeline (
                    timeline_id INT AUTO_INCREMENT PRIMARY KEY,
                    order_id INT NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    title VARCHAR(255) NOT NULL,
                    description TEXT,
                    tracking_number VARCHAR(100),
                    icon VARCHAR(50),
                    color VARCHAR(20),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders(order_id) ON DELETE CASCADE
                )
            ");
            
            // Get status info
            $statuses = getOrderStatuses();
            $status_info = $statuses[$status] ?? ['title' => ucfirst($status), 'icon' => '📦', 'color' => 'primary'];
            
            $stmt = $this->conn->prepare("
                INSERT INTO order_timeline (order_id, status, title, description, tracking_number, icon, color) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $order_id,
                $status,
                $status_info['title'],
                $notes ?: $status_info['description'] ?? '',
                $tracking_number,
                $status_info['icon'],
                $status_info['color']
            ]);
            
        } catch (Exception $e) {
            error_log("Error creating timeline entry: " . $e->getMessage());
        }
    }
    
    /**
     * Send order confirmation email
     */
    private function sendOrderConfirmationEmail($order_id, $user_id) {
        try {
            // Get user email
            $stmt = $this->conn->prepare("SELECT email, name FROM users WHERE user_id = ?");
            $stmt->execute([$user_id]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user && $user['email']) {
                // Create email notification (placeholder for actual email system)
                createRealTimeNotification($this->conn, $user_id, 'email', 
                    'Order Confirmation Email Sent', 
                    "Confirmation email has been sent to {$user['email']}", 
                    ['order_id' => $order_id, 'email' => $user['email']]
                );
            }
            
        } catch (Exception $e) {
            error_log("Error sending confirmation email: " . $e->getMessage());
        }
    }
    
    /**
     * Initialize real-time sync data
     */
    private function initializeRealTimeSync($order_id, $user_id) {
        try {
            storeRealTimeSyncData($this->conn, $order_id, null, 'pending', 'Order created', null, null, 'system');
        } catch (Exception $e) {
            error_log("Error initializing real-time sync: " . $e->getMessage());
        }
    }
    
    /**
     * Send status-specific notifications
     */
    private function sendStatusSpecificNotifications($order_id, $user_id, $old_status, $new_status, $tracking_number = null) {
        $messages = [
            'confirmed' => "✅ Your order #{$order_id} has been confirmed! We're preparing it for shipment.",
            'processing' => "⚙️ Your order #{$order_id} is being processed and will be shipped soon.",
            'shipped' => "🚚 Great news! Your order #{$order_id} has been shipped" . ($tracking_number ? " with tracking number: {$tracking_number}" : "") . ".",
            'delivered' => "🎉 Your order #{$order_id} has been delivered! Thank you for shopping with us.",
            'cancelled' => "❌ Your order #{$order_id} has been cancelled. If you have any questions, please contact support."
        ];
        
        if (isset($messages[$new_status])) {
            createRealTimeNotification($this->conn, $user_id, 'status_update', 
                "Order Status Update", 
                $messages[$new_status], 
                [
                    'order_id' => $order_id,
                    'old_status' => $old_status,
                    'new_status' => $new_status,
                    'tracking_number' => $tracking_number
                ]
            );
        }
    }
    
    /**
     * Update real-time sync
     */
    private function updateRealTimeSync($order_id, $old_status, $new_status, $notes, $tracking_number) {
        try {
            storeRealTimeSyncData($this->conn, $order_id, $old_status, $new_status, $notes, $tracking_number, null, 'admin');
        } catch (Exception $e) {
            error_log("Error updating real-time sync: " . $e->getMessage());
        }
    }
    
    /**
     * Get order timeline for display
     */
    public function getOrderTimeline($order_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM order_timeline 
                WHERE order_id = ? 
                ORDER BY created_at ASC
            ");
            $stmt->execute([$order_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting order timeline: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get order tracking info
     */
    public function getOrderTracking($order_id) {
        try {
            $stmt = $this->conn->prepare("
                SELECT * FROM order_tracking 
                WHERE order_id = ? 
                ORDER BY created_at DESC
            ");
            $stmt->execute([$order_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting order tracking: " . $e->getMessage());
            return [];
        }
    }
}

/**
 * Global helper functions for easy access
 */

/**
 * Initialize order management system
 */
function initializeOrderManagement($conn) {
    return new OrderManagementSystem($conn);
}

/**
 * Process new order (called from checkout.php)
 */
function processNewOrderComplete($order_id, $user_id, $conn, $order_data = []) {
    $orderManager = new OrderManagementSystem($conn);
    return $orderManager->processNewOrder($order_id, $user_id, $order_data);
}

/**
 * Update order status with full pipeline (called from admin)
 */
function updateOrderStatusComplete($order_id, $new_status, $admin_notes = '', $tracking_number = null, $conn = null) {
    global $conn;
    $connection = $conn;

    if (!$connection) {
        require_once __DIR__ . '/db_connect.php';
        $connection = $conn;
    }

    $orderManager = new OrderManagementSystem($connection);
    return $orderManager->updateOrderStatus($order_id, $new_status, $admin_notes, $tracking_number);
}
?>
