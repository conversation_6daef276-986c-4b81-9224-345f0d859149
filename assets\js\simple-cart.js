/**
 * Simple Cart JavaScript - Lightweight and Reliable
 * For TeWuNeed website
 */

console.log('🛒 Simple Cart JS loaded');

// Simple cart functionality
function simpleAddToCart(productId, button) {
    console.log('Adding product to cart:', productId);
    
    // Prevent double clicks
    if (button.disabled) {
        console.log('Button already disabled, ignoring click');
        return;
    }
    
    // Set loading state
    const originalText = button.innerHTML;
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
    
    // Create form data
    const formData = new FormData();
    formData.append('product_id', productId);
    formData.append('quantity', 1);
    
    // Send request
    fetch('ajax/simple_add_to_cart.php', {
        method: 'POST',
        body: formData,
        credentials: 'same-origin'
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers.get('content-type'));

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        // Check if response is JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            console.warn('Response is not JSON, getting text instead');
            return response.text().then(text => {
                console.log('Response text:', text.substring(0, 200));
                throw new Error('Server returned HTML instead of JSON. Check PHP errors.');
            });
        }

        return response.json();
    })
    .then(data => {
        console.log('Server response:', data);
        
        if (data.success) {
            // Success
            button.innerHTML = '<i class="fas fa-check"></i> Added!';
            button.classList.add('btn-success');

            // Update cart count - debug endpoint returns cart_count directly
            const cartCount = parseInt(data.cart_count) || 0;

            console.log('Raw response data:', data);
            console.log('Cart count:', cartCount);
            console.log('Debug info:', data.debug);

            updateCartCount(cartCount);

            // Show notification
            showSimpleNotification(data.message || 'Product added to cart!', 'success');

            // Reset button after 2 seconds
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('btn-success');
                button.disabled = false;
            }, 2000);

        } else {
            // Error from server
            throw new Error(data.message || 'Failed to add to cart');
        }
    })
    .catch(error => {
        console.error('Add to cart error:', error);
        console.error('Error details:', error.stack);

        // Show error with more details
        let errorMessage = 'Error: ' + error.message;
        if (error.message.includes('JSON')) {
            errorMessage = 'Server error: Please check if you are logged in and try again.';
        }

        showSimpleNotification(errorMessage, 'error');

        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Update cart count in UI
function updateCartCount(count) {
    console.log('Updating cart count to:', count);

    // Update all cart count elements
    const cartElements = document.querySelectorAll('.cart-count, .cart-badge, #cart-count');
    cartElements.forEach(element => {
        element.textContent = count;
        if (count > 0) {
            element.style.display = 'inline';
            element.classList.add('animate-bounce');
            setTimeout(() => element.classList.remove('animate-bounce'), 600);
        } else {
            element.style.display = 'none';
        }
    });

    // Also update cart link badge in header specifically
    const cartLink = document.querySelector("a[href='cart.php']");
    if (cartLink) {
        let cartBadge = cartLink.querySelector(".cart-count");

        if (count > 0) {
            if (!cartBadge) {
                cartBadge = document.createElement("span");
                cartBadge.className = "position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger cart-count";
                cartLink.appendChild(cartBadge);
            }
            cartBadge.textContent = count;
            cartBadge.style.display = 'inline';
        } else if (cartBadge) {
            cartBadge.style.display = 'none';
        }
    }

    // Clear session cache to force refresh on next page load
    fetch('ajax/clear_cart_cache.php', {
        method: 'POST',
        credentials: 'same-origin'
    }).catch(error => {
        console.log('Cache clear failed (non-critical):', error);
    });
}

// Simple notification system
function showSimpleNotification(message, type = 'info') {
    console.log('Showing notification:', message, type);
    
    // Remove existing notifications
    document.querySelectorAll('.simple-notification').forEach(n => n.remove());
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} simple-notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-radius: 8px;
    `;
    
    const icon = type === 'success' ? 'check-circle' : 
                type === 'error' ? 'exclamation-circle' : 'info-circle';
    
    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 10);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Simple cart initialized');

    // Check if universal handler is available
    if (window.universalProductHandler) {
        console.log('Universal handler detected, simple-cart will defer to it');
        return;
    }

    // Add click handlers to all add to cart buttons (except on product detail pages)
    document.addEventListener('click', function(e) {
        // Skip if we're on a product detail page or if universal handler exists
        if (window.location.pathname.includes('product-detail.php') ||
            window.location.href.includes('product-detail.php') ||
            window.universalProductHandler) {
            console.log('Skipping simple-cart handler - universal handler or product detail page');
            return;
        }

        // Handle add to cart buttons
        if (e.target.matches('.add-to-cart-btn, .btn-add-to-cart') ||
            e.target.closest('.add-to-cart-btn, .btn-add-to-cart')) {

            e.preventDefault();
            
            const button = e.target.matches('.add-to-cart-btn, .btn-add-to-cart') ? 
                          e.target : e.target.closest('.add-to-cart-btn, .btn-add-to-cart');
            
            // Get product ID
            const productId = button.dataset.productId || 
                             button.getAttribute('data-product-id') ||
                             button.closest('[data-product-id]')?.dataset.productId;
            
            if (productId) {
                console.log('Add to cart clicked for product:', productId);
                simpleAddToCart(productId, button);
            } else {
                console.error('Product ID not found');
                showSimpleNotification('Product ID not found', 'error');
            }
        }
    });
    
    // Load initial cart count
    loadCartCount();
});

// Load cart count from server
function loadCartCount() {
    fetch('ajax/get_cart_count.php', {
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success !== undefined) {
            updateCartCount(data.cart_count || 0);
        } else {
            // Handle old format
            updateCartCount(data.count || 0);
        }
    })
    .catch(error => {
        console.log('Could not load cart count:', error);
    });
}

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-bounce {
        animation: bounce 0.6s ease;
    }
    
    @keyframes bounce {
        0%, 20%, 60%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        80% { transform: translateY(-5px); }
    }
    
    .btn-success {
        background-color: #28a745 !important;
        border-color: #28a745 !important;
        color: white !important;
    }
    
    .simple-notification {
        pointer-events: auto;
    }
`;
document.head.appendChild(style);

// Global functions for backward compatibility
window.simpleAddToCart = simpleAddToCart;
window.updateCartCount = updateCartCount;
window.showSimpleNotification = showSimpleNotification;

console.log('✅ Simple cart functionality ready');
