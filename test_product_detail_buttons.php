<?php
/**
 * Test Product Detail Buttons
 * Comprehensive test for all button functionality on product detail page
 */

session_start();
require_once 'config.php';
require_once 'includes/db_connect.php';

// Get a sample product for testing
try {
    $stmt = $conn->prepare("SELECT * FROM products WHERE stock > 0 LIMIT 1");
    $stmt->execute();
    $test_product = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $test_product = null;
}

echo "<h1>🧪 Product Detail Buttons Test</h1>";
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.success { color: #28a745; }
.error { color: #dc3545; }
.info { color: #17a2b8; }
.warning { color: #ffc107; }
.test-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 8px; }
.test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
.test-card { background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-info { background: #17a2b8; color: white; }
</style>";

echo "<div class='test-section'>";
echo "<h2>📋 Test Overview</h2>";
echo "<p>This page tests all button functionality on the product detail page.</p>";

if ($test_product) {
    $product_name = $test_product['NAME'] ?? $test_product['name'] ?? 'Test Product';
    echo "<p class='success'>✅ Test product found: <strong>" . htmlspecialchars($product_name) . "</strong></p>";
    echo "<p class='info'>Product ID: {$test_product['product_id']} | Stock: {$test_product['stock']} | Price: Rp " . number_format($test_product['price']) . "</p>";
} else {
    echo "<p class='error'>❌ No test product found. Please add some products to the database first.</p>";
}
echo "</div>";

// Test 1: Button Elements Test
echo "<div class='test-section'>";
echo "<h2>🔘 Test 1: Button Elements</h2>";

$button_tests = [
    'Quantity Decrease' => ['id' => 'decrease-quantity', 'classes' => ['btn', 'btn-outline-secondary', 'decrease', 'btn-decrease']],
    'Quantity Increase' => ['id' => 'increase-quantity', 'classes' => ['btn', 'btn-outline-secondary', 'increase', 'btn-increase']],
    'Quantity Input' => ['id' => 'quantity', 'classes' => ['form-control', 'qty-input', 'quantity-input']],
    'Add to Cart' => ['classes' => ['btn', 'btn-primary', 'add-to-cart-btn', 'btn-add-to-cart']],
    'Wishlist' => ['classes' => ['btn', 'btn-outline-primary', 'wishlist-btn']],
    'Share' => ['classes' => ['btn', 'btn-outline-secondary', 'share-btn']]
];

echo "<table>";
echo "<tr><th>Button</th><th>Expected ID</th><th>Expected Classes</th><th>Status</th></tr>";

foreach ($button_tests as $name => $config) {
    echo "<tr>";
    echo "<td><strong>{$name}</strong></td>";
    echo "<td>" . ($config['id'] ?? 'N/A') . "</td>";
    echo "<td>" . implode(', ', $config['classes']) . "</td>";
    echo "<td><span class='success'>✅ Configured</span></td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Test 2: JavaScript Files Test
echo "<div class='test-section'>";
echo "<h2>📜 Test 2: JavaScript Files</h2>";

$js_files = [
    'assets/js/product-detail.js' => 'Enhanced Product Detail Handler',
    'assets/js/universal-product-handler.js' => 'Universal Product Handler',
    'assets/js/simple-cart.js' => 'Simple Cart Handler'
];

echo "<table>";
echo "<tr><th>File</th><th>Description</th><th>Status</th><th>Size</th></tr>";

foreach ($js_files as $file => $description) {
    echo "<tr>";
    echo "<td><code>{$file}</code></td>";
    echo "<td>{$description}</td>";
    
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<td class='success'>✅ Exists</td>";
        echo "<td>" . number_format($size) . " bytes</td>";
    } else {
        echo "<td class='error'>❌ Missing</td>";
        echo "<td>N/A</td>";
    }
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Test 3: AJAX Endpoint Test
echo "<div class='test-section'>";
echo "<h2>🔗 Test 3: AJAX Endpoints</h2>";

$ajax_files = [
    'ajax/simple_add_to_cart.php' => 'Add to Cart Handler',
    'ajax/add_to_wishlist.php' => 'Wishlist Handler (Optional)',
    'ajax/share_product.php' => 'Share Handler (Optional)'
];

echo "<table>";
echo "<tr><th>Endpoint</th><th>Description</th><th>Status</th></tr>";

foreach ($ajax_files as $file => $description) {
    echo "<tr>";
    echo "<td><code>{$file}</code></td>";
    echo "<td>{$description}</td>";
    
    if (file_exists($file)) {
        echo "<td class='success'>✅ Available</td>";
    } else {
        echo "<td class='warning'>⚠️ Optional</td>";
    }
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Test 4: Live Button Test
if ($test_product) {
    echo "<div class='test-section'>";
    echo "<h2>🎯 Test 4: Live Button Test</h2>";
    echo "<p>Test the actual buttons with the sample product:</p>";
    
    echo "<div class='test-grid'>";
    
    // Quantity Controls Test
    echo "<div class='test-card'>";
    echo "<h4>🔢 Quantity Controls</h4>";
    echo "<div class='mb-3'>";
    echo "<label class='form-label fw-bold'>Quantity:</label>";
    echo "<div class='input-group quantity-selector' style='max-width: 200px;'>";
    echo "<button class='btn btn-outline-secondary decrease btn-decrease' type='button' id='test-decrease' data-action='decrease'>";
    echo "<i class='fas fa-minus'></i>";
    echo "</button>";
    echo "<input type='number' id='test-quantity' class='form-control text-center qty-input quantity-input' value='1' min='1' max='{$test_product['stock']}' data-stock='{$test_product['stock']}'>";
    echo "<button class='btn btn-outline-secondary increase btn-increase' type='button' id='test-increase' data-action='increase'>";
    echo "<i class='fas fa-plus'></i>";
    echo "</button>";
    echo "</div>";
    echo "<small class='text-muted'>Available: <span class='fw-bold text-success'>{$test_product['stock']}</span> units</small>";
    echo "</div>";
    echo "</div>";
    
    // Action Buttons Test
    echo "<div class='test-card'>";
    echo "<h4>🎬 Action Buttons</h4>";
    echo "<div class='d-flex gap-2 flex-wrap'>";
    $product_name = $test_product['NAME'] ?? $test_product['name'] ?? 'Test Product';
    $product_name_escaped = htmlspecialchars($product_name, ENT_QUOTES);
    echo "<button class='btn btn-primary add-to-cart-btn btn-add-to-cart' data-product-id='{$test_product['product_id']}' data-product-name=\"{$product_name_escaped}\" data-product-price='{$test_product['price']}' data-stock='{$test_product['stock']}'>";
    echo "<i class='fas fa-shopping-cart me-2'></i>Add to Cart";
    echo "</button>";
    echo "<button class='btn btn-outline-primary wishlist-btn' data-product-id='{$test_product['product_id']}' title='Add to Wishlist'>";
    echo "<i class='fas fa-heart me-2'></i>Wishlist";
    echo "</button>";
    echo "<button class='btn btn-outline-secondary share-btn' data-product-id='{$test_product['product_id']}' data-product-name=\"{$product_name_escaped}\" title='Share Product'>";
    echo "<i class='fas fa-share-alt me-2'></i>Share";
    echo "</button>";
    echo "</div>";
    echo "</div>";
    
    // Keyboard Shortcuts Test
    echo "<div class='test-card'>";
    echo "<h4>⌨️ Keyboard Shortcuts</h4>";
    echo "<p>Test keyboard shortcuts (click in this area first):</p>";
    echo "<div tabindex='0' style='border: 2px dashed #ccc; padding: 20px; text-align: center; cursor: pointer;' onclick='this.focus()'>";
    echo "<p><strong>Click here and try these keys:</strong></p>";
    echo "<ul style='text-align: left; display: inline-block;'>";
    echo "<li><kbd>A</kbd> - Add to Cart</li>";
    echo "<li><kbd>W</kbd> - Toggle Wishlist</li>";
    echo "<li><kbd>S</kbd> - Share Product</li>";
    echo "<li><kbd>+</kbd> or <kbd>=</kbd> - Increase Quantity</li>";
    echo "<li><kbd>-</kbd> - Decrease Quantity</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
}

// Test 5: Browser Compatibility
echo "<div class='test-section'>";
echo "<h2>🌐 Test 5: Browser Compatibility</h2>";
echo "<p>JavaScript features used and their browser support:</p>";

$features = [
    'addEventListener' => 'All modern browsers',
    'fetch API' => 'IE 11+ (with polyfill), All modern browsers',
    'classList' => 'IE 10+, All modern browsers',
    'dataset' => 'IE 11+, All modern browsers',
    'querySelector' => 'IE 8+, All modern browsers',
    'ES6 Classes' => 'IE 11+ (with transpilation), All modern browsers',
    'Arrow Functions' => 'IE 11+ (with transpilation), All modern browsers',
    'Template Literals' => 'IE 11+ (with transpilation), All modern browsers'
];

echo "<table>";
echo "<tr><th>Feature</th><th>Browser Support</th><th>Status</th></tr>";

foreach ($features as $feature => $support) {
    echo "<tr>";
    echo "<td><code>{$feature}</code></td>";
    echo "<td>{$support}</td>";
    echo "<td class='success'>✅ Supported</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// Navigation Links
echo "<div class='test-section'>";
echo "<h2>🔗 Navigation</h2>";
echo "<div class='d-flex gap-2 flex-wrap'>";

if ($test_product) {
    echo "<a href='product-detail.php?id={$test_product['product_id']}' class='btn btn-primary'>View Actual Product Detail</a>";
}

echo "<a href='products_public.php' class='btn btn-info'>View Products Page</a>";
echo "<a href='cart.php' class='btn btn-success'>View Cart</a>";
echo "<a href='index.php' class='btn btn-warning'>Back to Home</a>";
echo "</div>";
echo "</div>";

// Include the JavaScript files for testing
echo "<script src='assets/js/product-detail.js'></script>";
echo "<script src='assets/js/universal-product-handler.js'></script>";
echo "<script src='assets/js/simple-cart.js'></script>";

// Alert container for notifications
echo "<div id='alert-container' class='position-fixed top-0 end-0 p-3' style='z-index: 1060;'></div>";

// Loading spinner
echo "<div id='spinner' class='position-fixed top-50 start-50 translate-middle' style='display: none; z-index: 1060'>";
echo "<div class='spinner-border text-primary' role='status'>";
echo "<span class='visually-hidden'>Loading...</span>";
echo "</div>";
echo "</div>";

// Bootstrap for styling
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js'></script>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";

echo "<script>";
echo "console.log('🧪 Product Detail Button Test Page Loaded');";
echo "console.log('Available handlers:', {";
echo "  productDetailHandler: !!window.productDetailHandler,";
echo "  universalProductHandler: !!window.universalProductHandler,";
echo "  EnhancedProductDetailHandler: !!window.EnhancedProductDetailHandler";
echo "});";
echo "</script>";
?>
