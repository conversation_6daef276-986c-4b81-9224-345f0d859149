<?php
/**
 * Test Product Detail Page
 * Quick test to verify all functionality is working
 */

session_start();
require_once 'includes/db_connect.php';

echo "<h2>🧪 Product Detail Test Page</h2>";
echo "<p>Testing product detail functionality...</p>";

// Check if user is logged in
if (!isset($_SESSION['user_id']) && !isset($_SESSION['firebase_user_id'])) {
    echo "<div style='color: red;'>❌ User not logged in. Please <a href='login.php'>login</a> first to test add to cart functionality.</div>";
} else {
    echo "<div style='color: green;'>✅ User is logged in</div>";
}

// Check if we have any products
try {
    $stmt = $conn->prepare("SELECT product_id, NAME, stock FROM products WHERE stock > 0 LIMIT 5");
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($products) {
        echo "<h3>Available Products for Testing:</h3>";
        echo "<ul>";
        foreach ($products as $product) {
            echo "<li><a href='product-detail.php?id=" . $product['product_id'] . "'>" .
                 htmlspecialchars($product['NAME']) . " (Stock: " . $product['stock'] . ")</a></li>";
        }
        echo "</ul>";

        // Auto-redirect to first product
        $test_product_id = $products[0]['product_id'];
        echo "<p>Auto-redirecting to first product in 3 seconds...</p>";
        echo "<script>setTimeout(() => window.location.href = 'product-detail.php?id=" . $test_product_id . "', 3000);</script>";
    } else {
        echo "<div style='color: red;'>❌ No products found in database</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Database error: " . $e->getMessage() . "</div>";
}
?>
